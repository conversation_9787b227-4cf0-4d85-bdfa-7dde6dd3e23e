import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

// GET endpoint to fetch products with filtering and pagination
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const minPrice = searchParams.get('minPrice');
    const maxPrice = searchParams.get('maxPrice');
    const condition = searchParams.get('condition');
    const location = searchParams.get('location');
    const attributes = searchParams.get('attributes');
    const offset = (page - 1) * limit;

    // Build where clause
    const where: any = {
      status: 'ACTIVE', // Only show active products
      isHidden: false,
    };

    if (category && category !== 'all') {
      where.category = {
        slug: category,
      };
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (minPrice || maxPrice) {
      where.price = {};
      if (minPrice) where.price.gte = parseFloat(minPrice);
      if (maxPrice) where.price.lte = parseFloat(maxPrice);
    }

    if (condition) {
      where.condition = condition;
    }

    if (location) {
      where.location = {
        OR: [
          { city: { contains: location, mode: 'insensitive' } },
          { state: { contains: location, mode: 'insensitive' } },
          { address: { contains: location, mode: 'insensitive' } },
        ],
      };
    }

    // Handle attribute filtering
    if (attributes) {
      try {
        const attributeFilters = JSON.parse(attributes);
        const attributeConditions = [];

        for (const [key, value] of Object.entries(attributeFilters)) {
          if (value !== null && value !== undefined && value !== '') {
            // For JSON field queries in Prisma, we need to use path syntax
            attributeConditions.push({
              attributes: {
                path: [key],
                equals: value,
              },
            });
          }
        }

        if (attributeConditions.length > 0) {
          where.AND = attributeConditions;
        }
      } catch (error) {
        console.error('Error parsing attribute filters:', error);
      }
    }

    // Fetch products with pagination
    const [products, totalCount] = await Promise.all([
      prisma.product.findMany({
        where,
        include: {
          seller: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          location: true,
          _count: {
            select: {
              reviews: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: offset,
        take: limit,
      }),
      prisma.product.count({ where }),
    ]);

    return NextResponse.json({
      products,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST endpoint to create a new product
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      name,
      description,
      price,
      categoryId,
      condition,
      images,
      location,
      attributes,
      status,
    } = body;

    // Validate required fields
    if (!name || !price || !categoryId) {
      return NextResponse.json(
        { message: 'Name, price, and category are required' },
        { status: 400 }
      );
    }

    // Validate price
    const numericPrice = parseFloat(price);
    if (isNaN(numericPrice) || numericPrice <= 0) {
      return NextResponse.json(
        { message: 'Price must be a valid positive number' },
        { status: 400 }
      );
    }

    // Check if category exists
    const category = await prisma.category.findUnique({
      where: { id: categoryId },
    });

    if (!category) {
      return NextResponse.json(
        { message: 'Category not found' },
        { status: 404 }
      );
    }

    // Handle location if provided
    let locationId = null;
    if (location && location.address) {
      // Check if location already exists
      const existingLocation = await prisma.location.findFirst({
        where: {
          address: location.address,
          city: location.city,
          state: location.state,
          country: location.country,
        },
      });

      if (existingLocation) {
        locationId = existingLocation.id;
      } else {
        // Create new location
        const newLocation = await prisma.location.create({
          data: {
            address: location.address,
            city: location.city,
            state: location.state || '',
            country: location.country,
            postalCode: location.postalCode || '',
            latitude: location.latitude || 0,
            longitude: location.longitude || 0,
          },
        });
        locationId = newLocation.id;
      }
    }

    // Create the product
    const product = await prisma.product.create({
      data: {
        name,
        description: description || null,
        price: numericPrice,
        categoryId,
        condition: condition || 'new',
        images: images || [],
        sellerId: session.user.id,
        locationId,
        status: status || 'ACTIVE', // Use provided status or default to ACTIVE
        // Note: attributes would need a separate table/handling based on your schema
      },
      include: {
        seller: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
            icon: true,
          },
        },
        location: true,
      },
    });

    return NextResponse.json(product, { status: 201 });
  } catch (error) {
    console.error('Error creating product:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

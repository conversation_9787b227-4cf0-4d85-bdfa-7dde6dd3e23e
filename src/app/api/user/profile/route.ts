import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { prisma } from '@/lib/prisma';

// GET /api/user/profile - Get the current user's profile
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Fetch user data with follow counts
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        seller: {
          select: {
            id: true,
          },
        },
        _count: {
          select: {
            followers: true,
            following: true,
          },
        },
      },
    });

    if (!user) {
      return new NextResponse('User not found', { status: 404 });
    }

    // Format the data for the frontend
    const userProfile = {
      id: user.id,
      name: user.name,
      email: user.email,
      image: user.image || '',
      phone: user.phone || '',
      bio: user.bio || '',
      address: user.address || '',
      dateOfBirth: user.dateOfBirth?.toISOString() || '',
      isVerified: user.isVerified,
      createdAt: user.createdAt.toISOString(),
      isSeller: !!user.seller,
      followingCount: user._count.following,
      followersCount: user._count.followers,
    };

    return NextResponse.json(userProfile);
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}

// PUT /api/user/profile - Update the current user's profile
export async function PUT(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const body = await request.json();
    const { name, email, phone, bio, address, dateOfBirth, image } = body;

    // Validate required fields
    if (email && !email.includes('@')) {
      return NextResponse.json(
        { message: 'Invalid email address' },
        { status: 400 }
      );
    }

    // Prepare update data
    const updateData: any = {};
    if (name !== undefined) updateData.name = name;
    if (email !== undefined) updateData.email = email;
    if (phone !== undefined) updateData.phone = phone;
    if (bio !== undefined) updateData.bio = bio;
    if (address !== undefined) updateData.address = address;
    if (image !== undefined) updateData.image = image;
    if (dateOfBirth !== undefined) {
      updateData.dateOfBirth = dateOfBirth ? new Date(dateOfBirth) : null;
    }

    // Update the user
    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: {
        ...updateData,
        updatedAt: new Date(),
      },
      include: {
        seller: {
          select: {
            id: true,
          },
        },
        _count: {
          select: {
            followers: true,
            following: true,
          },
        },
      },
    });

    // Format the response
    const userProfile = {
      id: updatedUser.id,
      name: updatedUser.name,
      email: updatedUser.email,
      image: updatedUser.image || '',
      phone: updatedUser.phone || '',
      bio: updatedUser.bio || '',
      address: updatedUser.address || '',
      dateOfBirth: updatedUser.dateOfBirth?.toISOString() || '',
      isVerified: updatedUser.isVerified,
      createdAt: updatedUser.createdAt.toISOString(),
      isSeller: !!updatedUser.seller,
      followingCount: updatedUser._count.following,
      followersCount: updatedUser._count.followers,
    };

    return NextResponse.json(userProfile);
  } catch (error) {
    console.error('Error updating user profile:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params;

    // Find the category
    const category = await prisma.category.findUnique({
      where: { slug },
    });

    if (!category) {
      return NextResponse.json({ error: 'Category not found' }, { status: 404 });
    }

    // Get category attributes from the database
    const categoryAttributes = await prisma.categoryAttribute.findMany({
      where: { categoryId: category.id },
      orderBy: { name: 'asc' },
    });

    // If no attributes found in database, return hardcoded attributes for demo
    if (categoryAttributes.length === 0) {
      const hardcodedAttributes = getHardcodedAttributes(slug);
      return NextResponse.json(hardcodedAttributes);
    }

    // Transform database attributes to expected format
    const transformedAttributes = categoryAttributes.map(attr => ({
      id: attr.name.toLowerCase().replace(/\s+/g, ''),
      name: attr.name,
      type: attr.type.toLowerCase(),
      options: attr.options || [],
      isRequired: attr.isRequired,
    }));

    return NextResponse.json(transformedAttributes);
  } catch (error) {
    console.error('Error fetching category attributes:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function getHardcodedAttributes(categorySlug: string) {
  if (categorySlug.includes('real-estate') || categorySlug.includes('houses') || categorySlug.includes('apartments')) {
    return [
      {
        id: 'bedrooms',
        name: 'Bedrooms',
        type: 'number',
        options: [],
        isRequired: false,
      },
      {
        id: 'bathrooms',
        name: 'Bathrooms',
        type: 'number',
        options: [],
        isRequired: false,
      },
      {
        id: 'squarefeet',
        name: 'Square Feet',
        type: 'number',
        options: [],
        isRequired: false,
      },
      {
        id: 'propertytype',
        name: 'Property Type',
        type: 'select',
        options: ['Single Family', 'Duplex', 'Apartment', 'Townhouse', 'Condo', 'Commercial'],
        isRequired: false,
      },
      {
        id: 'furnished',
        name: 'Furnished',
        type: 'boolean',
        options: [],
        isRequired: false,
      },
      {
        id: 'parking',
        name: 'Parking Available',
        type: 'boolean',
        options: [],
        isRequired: false,
      },
      {
        id: 'pool',
        name: 'Swimming Pool',
        type: 'boolean',
        options: [],
        isRequired: false,
      },
    ];
  } else if (categorySlug.includes('vehicles') || categorySlug.includes('cars')) {
    return [
      {
        id: 'make',
        name: 'Make',
        type: 'select',
        options: ['Toyota', 'Honda', 'BMW', 'Mercedes-Benz', 'Lexus', 'Audi', 'Ford', 'Chevrolet', 'Nissan', 'Hyundai'],
        isRequired: false,
      },
      {
        id: 'year',
        name: 'Year',
        type: 'number',
        options: [],
        isRequired: false,
      },
      {
        id: 'mileage',
        name: 'Mileage',
        type: 'number',
        options: [],
        isRequired: false,
      },
      {
        id: 'fueltype',
        name: 'Fuel Type',
        type: 'select',
        options: ['Gasoline', 'Diesel', 'Electric', 'Hybrid', 'Plug-in Hybrid'],
        isRequired: false,
      },
      {
        id: 'transmission',
        name: 'Transmission',
        type: 'select',
        options: ['Automatic', 'Manual', 'CVT'],
        isRequired: false,
      },
      {
        id: 'drivetrain',
        name: 'Drivetrain',
        type: 'select',
        options: ['FWD', 'RWD', 'AWD', '4WD'],
        isRequired: false,
      },
    ];
  } else if (categorySlug.includes('gadgets') || categorySlug.includes('smartphones') || categorySlug.includes('laptops')) {
    return [
      {
        id: 'brand',
        name: 'Brand',
        type: 'select',
        options: ['Apple', 'Samsung', 'Google', 'OnePlus', 'Dell', 'HP', 'Lenovo', 'ASUS', 'Microsoft'],
        isRequired: false,
      },
      {
        id: 'storage',
        name: 'Storage',
        type: 'select',
        options: ['64GB', '128GB', '256GB', '512GB', '1TB', '2TB'],
        isRequired: false,
      },
      {
        id: 'ram',
        name: 'RAM',
        type: 'select',
        options: ['4GB', '8GB', '16GB', '32GB', '64GB'],
        isRequired: false,
      },
      {
        id: 'screensize',
        name: 'Screen Size',
        type: 'select',
        options: ['5.5"', '6.1"', '6.7"', '13"', '14"', '15"', '16"'],
        isRequired: false,
      },
      {
        id: 'warranty',
        name: 'Warranty',
        type: 'boolean',
        options: [],
        isRequired: false,
      },
      {
        id: 'unlocked',
        name: 'Unlocked',
        type: 'boolean',
        options: [],
        isRequired: false,
      },
    ];
  }

  return [];
}

'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ChatWindow from '@/components/ChatWindow';
import { categories } from '@/lib/data';
import { useSocket } from '@/hooks/useSocket';
import {
  ChatBubbleLeftRightIcon,
  MagnifyingGlassIcon,
} from '@heroicons/react/24/outline';

interface Chat {
  id: string;
  product: {
    id: string;
    name: string;
    price: number;
    images: string[];
    status: string;
  };
  buyer: {
    id: string;
    name: string;
    image?: string;
  };
  seller: {
    id: string;
    name: string;
    image?: string;
  };
  messages: Array<{
    id: string;
    content: string;
    createdAt: string;
    sender: {
      id: string;
      name: string;
      image?: string;
    };
  }>;
  _count: {
    messages: number;
  };
  updatedAt: string;
}

export default function MessagesPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [chats, setChats] = useState<Chat[]>([]);
  const [selectedChat, setSelectedChat] = useState<Chat | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [chatSearchQuery, setChatSearchQuery] = useState('');

  const { isConnected, on, off } = useSocket();

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'loading') return;
    if (!session) {
      router.push('/auth/signin?callbackUrl=/messages');
    }
  }, [session, status, router]);

  // Fetch chats and handle chatId from URL
  useEffect(() => {
    if (session?.user) {
      fetchChats();

      // Check if there's a chatId in the URL
      const urlParams = new URLSearchParams(window.location.search);
      const chatId = urlParams.get('chatId');
      if (chatId) {
        // Find and select the chat after chats are loaded
        setTimeout(() => {
          const chat = chats.find(c => c.id === chatId);
          if (chat) {
            setSelectedChat(chat);
          }
        }, 1000);
      }
    }
  }, [session, chats]);

  // Listen for real-time updates
  useEffect(() => {
    const handleNewMessage = (message: any) => {
      setChats(prevChats =>
        prevChats.map(chat =>
          chat.id === message.chatId
            ? {
                ...chat,
                messages: [message],
                updatedAt: message.createdAt,
                _count: {
                  ...chat._count,
                  messages: message.senderId !== session?.user?.id
                    ? chat._count.messages + 1
                    : chat._count.messages
                }
              }
            : chat
        )
      );
    };

    if (isConnected) {
      on('new-message', handleNewMessage);
    }

    return () => {
      if (isConnected) {
        off('new-message', handleNewMessage);
      }
    };
  }, [isConnected, session?.user?.id, on, off]);

  const fetchChats = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/chats');
      if (response.ok) {
        const data = await response.json();
        setChats(data);
      }
    } catch (error) {
      console.error('Error fetching chats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredChats = chats.filter(chat => {
    const otherUser = chat.buyer.id === session?.user?.id ? chat.seller : chat.buyer;
    return (
      otherUser.name.toLowerCase().includes(chatSearchQuery.toLowerCase()) ||
      chat.product.name.toLowerCase().includes(chatSearchQuery.toLowerCase())
    );
  });

  const formatLastMessageTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <Header
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        isMenuOpen={isMenuOpen}
        setIsMenuOpen={setIsMenuOpen}
      />

      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Messages</h1>

          <div className="bg-white rounded-xl shadow-sm overflow-hidden" style={{ height: '600px' }}>
            <div className="flex h-full">
              {/* Chat List */}
              <div className="w-1/3 border-r border-gray-200 flex flex-col">
                {/* Search */}
                <div className="p-4 border-b border-gray-200">
                  <div className="relative">
                    <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search conversations..."
                      value={chatSearchQuery}
                      onChange={(e) => setChatSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    />
                  </div>
                </div>

                {/* Chat List */}
                <div className="flex-1 overflow-y-auto">
                  {isLoading ? (
                    <div className="flex justify-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                    </div>
                  ) : filteredChats.length === 0 ? (
                    <div className="p-6 text-center">
                      <ChatBubbleLeftRightIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500">No conversations yet</p>
                      <button
                        onClick={() => router.push('/')}
                        className="mt-4 text-indigo-600 hover:text-indigo-700 font-medium"
                      >
                        Browse Products
                      </button>
                    </div>
                  ) : (
                    filteredChats.map((chat) => {
                      const otherUser = chat.buyer.id === session?.user?.id ? chat.seller : chat.buyer;
                      const lastMessage = chat.messages[0];
                      const unreadCount = chat._count.messages;

                      return (
                        <div
                          key={chat.id}
                          onClick={() => setSelectedChat(chat)}
                          className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                            selectedChat?.id === chat.id ? 'bg-indigo-50 border-indigo-200' : ''
                          }`}
                        >
                          <div className="flex items-center space-x-3">
                            <div className="relative">
                              {otherUser.image ? (
                                <Image
                                  src={otherUser.image}
                                  alt={otherUser.name}
                                  width={48}
                                  height={48}
                                  className="rounded-full object-cover"
                                />
                              ) : (
                                <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center text-indigo-600 font-semibold">
                                  {otherUser.name.charAt(0)}
                                </div>
                              )}
                              {unreadCount > 0 && (
                                <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                                  {unreadCount > 9 ? '9+' : unreadCount}
                                </div>
                              )}
                            </div>

                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <p className="font-semibold text-gray-900 truncate">{otherUser.name}</p>
                                {lastMessage && (
                                  <span className="text-xs text-gray-500">
                                    {formatLastMessageTime(lastMessage.createdAt)}
                                  </span>
                                )}
                              </div>
                              <p className="text-sm text-gray-600 truncate">{chat.product.name}</p>
                              {lastMessage && (
                                <p className="text-sm text-gray-500 truncate mt-1">
                                  {lastMessage.sender.id === session?.user?.id ? 'You: ' : ''}
                                  {lastMessage.content}
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })
                  )}
                </div>
              </div>

              {/* Chat Window */}
              <div className="flex-1">
                {selectedChat ? (
                  <ChatWindow
                    chat={selectedChat}
                    onClose={() => setSelectedChat(null)}
                  />
                ) : (
                  <div className="h-full flex items-center justify-center text-gray-500">
                    <div className="text-center">
                      <ChatBubbleLeftRightIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                      <p className="text-lg">Select a conversation to start messaging</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer categories={categories} />
    </div>
  );
}

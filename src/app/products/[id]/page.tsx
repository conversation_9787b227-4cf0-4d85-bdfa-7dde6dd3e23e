import React from 'react';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { prisma } from '@/lib/prisma';
import Image from 'next/image';
import StartChatButton from '@/components/StartChatButton';
import FollowButtonClient from '@/components/FollowButtonClient';
import ShareButtonClient from '@/components/ShareButtonClient';
import ProductReviews from '@/components/ProductReviews';
import SafetyTips from '@/components/SafetyTips';
import ProductAttributes from '@/components/ProductAttributes';
import CategorySafetyTips from '@/components/CategorySafetyTips';
import Footer from '@/components/Footer';

async function getProduct(id: string) {
  const product = await prisma.product.findUnique({
    where: { id },
    include: {
      seller: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      category: true,
      location: true,
    },
  });

  if (!product) return null;

  // Get seller profile if exists
  const sellerProfile = await prisma.seller.findUnique({
    where: { userId: product.seller.id },
    select: {
      businessName: true,
      rating: true,
      idVerificationStatus: true,
    },
  });

  return {
    ...product,
    seller: {
      ...product.seller,
      businessName: sellerProfile?.businessName,
      rating: sellerProfile?.rating || 0,
      verificationStatus: sellerProfile?.idVerificationStatus || 'PENDING',
    },
  };
}

export default async function ProductPage({
  params,
}: {
  params: { id: string };
}) {
  const session = await getServerSession(authOptions);
  const product = await getProduct(params.id);

  if (!product) {
    return <div>Product not found</div>;
  }

  // Track product view if user is logged in
  if (session?.user?.id) {
    try {
      await prisma.productView.upsert({
        where: {
          userId_productId: {
            userId: session.user.id,
            productId: product.id,
          },
        },
        update: {
          updatedAt: new Date(),
        },
        create: {
          userId: session.user.id,
          productId: product.id,
        },
      });

      // Increment view count
      await prisma.product.update({
        where: { id: product.id },
        data: {
          viewCount: {
            increment: 1,
          },
        },
      });
    } catch (error) {
      // Silently fail view tracking
      console.error('Failed to track product view:', error);
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Product Images */}
          <div className="space-y-4">
            <div className="relative aspect-square">
              <Image
                src={product.images?.[0] || '/placeholder-product.jpg'}
                alt={product.name}
                fill
                className="object-cover rounded-lg"
              />
            </div>
            <div className="grid grid-cols-4 gap-4">
              {product.images?.slice(1).map((image, index) => (
                <div key={index} className="relative aspect-square">
                  <Image
                    src={image}
                    alt={`${product.name} - Image ${index + 2}`}
                    fill
                    className="object-cover rounded-lg"
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Product Details */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold">{product.name}</h1>
              <p className="text-2xl font-semibold text-indigo-600 mt-2">
                ${product.price.toFixed(2)}
              </p>
              <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                <span>{product.viewCount || 0} views</span>
                <span>•</span>
                <span className={`px-2 py-1 rounded text-xs ${
                  product.condition === 'NEW'
                    ? 'bg-green-100 text-green-800'
                    : product.condition === 'REFURBISHED'
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {product.condition}
                </span>
                {product.featuredUntil && new Date(product.featuredUntil) > new Date() && (
                  <>
                    <span>•</span>
                    <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs">
                      Featured
                    </span>
                  </>
                )}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center">
                  <span className="text-indigo-600 font-semibold text-lg">
                    {(product.seller.businessName || product.seller.name || 'U').charAt(0).toUpperCase()}
                  </span>
                </div>
                <div>
                  <h3 className="font-medium">
                    {product.seller.businessName || product.seller.name}
                  </h3>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500">
                      Rating: {product.seller.rating ? product.seller.rating.toFixed(1) : 'New Seller'}
                    </span>
                    {product.seller.verificationStatus === 'VERIFIED' && (
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                        Verified
                      </span>
                    )}
                  </div>
                </div>
              </div>
              <FollowButtonClient sellerId={product.seller.id} />
            </div>

            <div className="prose max-w-none">
              <p>{product.description}</p>
            </div>

            <div className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">Category</h3>
                <p className="text-gray-600">{product.category.name}</p>
              </div>

              <div>
                <h3 className="font-medium mb-2">Condition</h3>
                <p className="text-gray-600">{product.condition}</p>
              </div>

              {product.location && (
                <div>
                  <h3 className="font-medium mb-2">Location</h3>
                  <p className="text-gray-600">
                    {typeof product.location === 'string'
                      ? product.location
                      : `${product.location.city}, ${product.location.state}, ${product.location.country}`
                    }
                  </p>
                </div>
              )}
            </div>

            <div className="pt-6 space-y-4">
              <div className="flex items-center space-x-4">
                <StartChatButton
                  productId={product.id}
                  sellerId={product.seller.id}
                  className="flex-1"
                />
                <ShareButtonClient
                  title={product.name}
                  description={product.description || undefined}
                />
              </div>
              <div className="text-sm text-gray-500 flex items-center justify-end">
                <span>{product.shareCount || 0} shares</span>
              </div>
            </div>
          </div>
        </div>

        {/* Product Attributes Section */}
        {product.attributes && Object.keys(product.attributes).length > 0 && (
          <div className="mt-8">
            <ProductAttributes
              attributes={product.attributes}
              categorySlug={product.category.slug}
              categoryName={product.category.name}
            />
          </div>
        )}

        {/* Safety Tips Section */}
        <div className="mt-8">
          <CategorySafetyTips
            categorySlug={product.category.slug}
            categoryName={product.category.name}
          />
        </div>

        {/* Reviews Section */}
        <div className="mt-8">
          <ProductReviews
            productId={product.id}
            sellerId={product.seller.id}
          />
        </div>
      </div>
      <Footer />
    </div>
  );
}
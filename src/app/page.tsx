import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { prisma } from '@/lib/prisma';
import Link from 'next/link';
import Image from 'next/image';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { StarIcon, CheckIcon, ChatIcon, LocationIcon } from '@/components/Icons';
import Newsletter from '@/components/Newsletter';
import FAQ from '@/components/FAQ';
import PersonalizedRecommendations from '@/components/PersonalizedRecommendations';
import NearbyProductsSection from '@/components/NearbyProductsSection';

async function getFeaturedProducts() {
  return prisma.product.findMany({
    where: {
      status: 'ACTIVE',
      isHidden: false,
      OR: [
        { featuredUntil: { gte: new Date() } },
        { viewCount: { gte: 50 } }, // Popular products
      ],
    },
    include: {
      seller: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      category: {
        select: {
          id: true,
          name: true,
          slug: true,
        },
      },
    },
    orderBy: [
      { featuredUntil: 'desc' },
      { viewCount: 'desc' },
    ],
    take: 8,
  });
}

async function getCategories() {
  const categories = await prisma.category.findMany({
    include: {
      _count: {
        select: {
          products: {
            where: {
              status: 'ACTIVE',
              isHidden: false,
            },
          },
        },
      },
    },
    orderBy: {
      name: 'asc',
    },
    take: 6,
  });

  return categories.map(category => ({
    ...category,
    count: category._count.products,
    icon: getCategoryIcon(category.name),
  }));
}

async function getTopSellers() {
  const sellers = await prisma.seller.findMany({
    where: {
      idVerificationStatus: 'VERIFIED',
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
          _count: {
            select: {
              products: {
                where: {
                  status: 'ACTIVE',
                  isHidden: false,
                },
              },
            },
          },
        },
      },
      _count: {
        select: {
          socialAccounts: true,
        },
      },
    },
    orderBy: {
      rating: 'desc',
    },
    take: 3,
  });

  return sellers.map(seller => ({
    id: seller.id,
    name: seller.businessName || seller.user.name,
    rating: seller.rating,
    activeListings: seller.user._count.products,
    joined: seller.createdAt.toLocaleDateString(),
    location: 'Nigeria', // Default location
  }));
}

function getCategoryIcon(categoryName: string): string {
  const iconMap: { [key: string]: string } = {
    'Electronics': '📱',
    'Fashion': '👗',
    'Vehicles': '🚗',
    'Real Estate': '🏠',
    'Home & Garden': '🏡',
    'Sports': '⚽',
    'Books': '📚',
    'Collectibles': '🎨',
    'Tools': '🔧',
    'Furniture': '🪑',
  };

  return iconMap[categoryName] || '📦';
}

export default async function Home() {
  const session = await getServerSession(authOptions);
  const [featuredProducts, categories, topSellers] = await Promise.all([
    getFeaturedProducts(),
    getCategories(),
    getTopSellers(),
  ]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <Header />

      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <section className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Find Your Perfect <span className="text-indigo-600">Electronics</span>, <span className="text-indigo-600">Vehicle</span>, or <span className="text-indigo-600">Fashion</span>
          </h1>
          <p className="text-xl text-slate-600 mb-8">
            Connect directly with trusted sellers worldwide
          </p>
          <div className="flex justify-center space-x-4">
            <Link
              href="/search"
              className="bg-indigo-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-indigo-700 transition"
            >
              Browse Products
            </Link>
            <Link
              href="/sell"
              className="bg-white text-indigo-600 px-8 py-3 rounded-lg font-semibold border border-indigo-600 hover:bg-indigo-50 transition"
            >
              Start Selling
            </Link>
          </div>
        </section>

        {/* Categories Section */}
        <section id="categories" className="mb-16">
          <h2 className="text-3xl font-bold mb-8 text-center">Popular Categories</h2>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 md:gap-6 max-w-4xl mx-auto">
            {categories.map((category) => (
              <Link
                key={category.id}
                href={`/search?category=${category.slug}`}
                className="group p-4 md:p-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-slate-100"
              >
                <div className="text-4xl mb-3 group-hover:scale-110 transition-transform">{category.icon}</div>
                <h3 className="font-semibold text-lg mb-1">{category.name}</h3>
                <p className="text-sm text-slate-500">{category.count} items</p>
              </Link>
            ))}
          </div>
        </section>

        {/* Featured Products */}
        <section id="products" className="mb-16">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-3xl font-bold">Featured Products</h2>
            <Link href="/search" className="text-indigo-600 font-medium hover:underline">View all →</Link>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredProducts.map((product) => (
              <Link key={product.id} href={`/products/${product.id}`}>
                <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden">
                  <div className="relative aspect-square">
                    <Image
                      src={product.images[0] || '/placeholder-product.jpg'}
                      alt={product.name}
                      fill
                      className="object-cover"
                    />
                    {product.featuredUntil && new Date(product.featuredUntil) > new Date() && (
                      <div className="absolute top-2 left-2 bg-yellow-500 text-white px-2 py-1 rounded text-xs font-semibold">
                        Featured
                      </div>
                    )}
                    <div className={`absolute top-2 right-2 px-2 py-1 rounded text-xs font-semibold ${
                      product.condition === 'NEW'
                        ? 'bg-green-500 text-white'
                        : product.condition === 'REFURBISHED'
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-500 text-white'
                    }`}>
                      {product.condition}
                    </div>
                  </div>

                  <div className="p-4">
                    <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                      {product.name}
                    </h3>
                    <p className="text-2xl font-bold text-indigo-600 mb-2">
                      ${product.price.toFixed(2)}
                    </p>
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <span>{product.seller.name}</span>
                      <span>{product.viewCount || 0} views</span>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </section>

        {/* Featured Sellers */}
        <section id="sellers" className="mb-16">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-3xl font-bold">Top Sellers</h2>
            <Link href="/search" className="text-indigo-600 font-medium hover:underline">View all →</Link>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {topSellers.map((seller) => (
              <div key={seller.id} className="bg-white rounded-xl shadow-sm p-6">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center text-indigo-600 font-bold text-xl mr-4">
                    {seller.name.charAt(0)}
                  </div>
                  <div>
                    <h3 className="font-semibold">{seller.name}</h3>
                    <div className="flex items-center">
                      <StarIcon className="w-4 h-4 text-yellow-400" />
                      <span className="ml-1 text-sm">{seller.rating.toFixed(1)}</span>
                      <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                        Verified
                      </span>
                    </div>
                  </div>
                </div>
                <div className="space-y-2 text-sm text-slate-600">
                  <p>Active Listings: {seller.activeListings}</p>
                  <p>Joined: {seller.joined}</p>
                  <p>Location: {seller.location}</p>
                </div>
                <Link
                  href={`/sellers/${seller.id}`}
                  className="mt-4 block text-center py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition"
                >
                  View Profile
                </Link>
              </div>
            ))}
          </div>
        </section>

        {/* Personalized Recommendations Section */}
        {session && (
          <section id="recommendations" className="mb-16">
            <PersonalizedRecommendations />
          </section>
        )}

        {/* Newsletter Section */}
        <div className="mb-12">
          <Newsletter />
        </div>

        {/* FAQ Section */}
        <div className="mb-12">
          <FAQ />
        </div>
      </main>

      <Footer />
    </div>
  );
}
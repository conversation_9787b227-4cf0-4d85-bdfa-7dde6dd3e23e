'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AvatarUpload from '@/components/AvatarUpload';
import PhoneVerification from '@/components/PhoneVerification';
import IdVerification from '@/components/IdVerification';
import { categories } from '@/lib/data';
import {
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  CalendarIcon,
  DocumentTextIcon,
  ShieldCheckIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';

interface UserProfile {
  id: string;
  name: string;
  email: string;
  image: string;
  phone: string;
  bio: string;
  address: string;
  dateOfBirth: string;
  isVerified: boolean;
  createdAt: string;
}

export default function SettingsPage() {
  const { data: session, status, update } = useSession();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'profile' | 'verification'>('profile');
  const [verificationStep, setVerificationStep] = useState<'overview' | 'phone' | 'id'>('overview');
  const [idVerificationStatus, setIdVerificationStatus] = useState<any>(null);

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    bio: '',
    address: '',
    dateOfBirth: '',
  });

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'loading') return;
    if (!session) {
      router.push('/auth/signin?callbackUrl=/account/settings');
    }
  }, [session, status, router]);

  // Fetch user profile and verification status
  useEffect(() => {
    if (session?.user) {
      fetchProfile();
      fetchIdVerificationStatus();
    }
  }, [session]);

  const fetchProfile = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/user/profile');

      if (!response.ok) {
        throw new Error('Failed to fetch profile');
      }

      const data = await response.json();
      setProfile(data);
      setFormData({
        name: data.name || '',
        email: data.email || '',
        phone: data.phone || '',
        bio: data.bio || '',
        address: data.address || '',
        dateOfBirth: data.dateOfBirth ? data.dateOfBirth.split('T')[0] : '',
      });
    } catch (err: any) {
      setError(err.message || 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    setError(null);
    setSuccessMessage(null);

    try {
      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update profile');
      }

      const updatedProfile = await response.json();
      setProfile(updatedProfile);
      setSuccessMessage('Profile updated successfully!');

      // Update session if name or email changed
      if (formData.name !== session?.user?.name || formData.email !== session?.user?.email) {
        await update({
          name: formData.name,
          email: formData.email,
        });
      }
    } catch (err: any) {
      setError(err.message || 'Failed to update profile');
    } finally {
      setIsSaving(false);
    }
  };

  const fetchIdVerificationStatus = async () => {
    try {
      const response = await fetch('/api/verification/id');
      if (response.ok) {
        const data = await response.json();
        setIdVerificationStatus(data);
      }
    } catch (error) {
      console.error('Error fetching ID verification status:', error);
    }
  };

  const handleAvatarUpdate = (imageUrl: string) => {
    if (profile) {
      setProfile({ ...profile, image: imageUrl });
      setSuccessMessage('Profile picture updated successfully!');
    }
  };

  const handlePhoneVerified = () => {
    setSuccessMessage('Phone number verified successfully!');
    setVerificationStep('overview');
    fetchProfile(); // Refresh profile to get updated verification status
  };

  const handleIdSubmitted = () => {
    setSuccessMessage('ID verification submitted successfully! We will review it within 1-3 business days.');
    setVerificationStep('overview');
    fetchIdVerificationStatus(); // Refresh ID verification status
  };

  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <Header
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        isMenuOpen={isMenuOpen}
        setIsMenuOpen={setIsMenuOpen}
      />

      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Account Settings</h1>
            <p className="text-gray-600">Manage your profile and account preferences</p>
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-200 mb-6">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('profile')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'profile'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Profile Information
              </button>
              <button
                onClick={() => setActiveTab('verification')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'verification'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Verification
              </button>
            </nav>
          </div>

          {/* Messages */}
          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-600">{error}</p>
            </div>
          )}

          {successMessage && (
            <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
              <p className="text-green-600">{successMessage}</p>
            </div>
          )}

          {/* Profile Tab */}
          {activeTab === 'profile' && profile && (
            <div className="bg-white rounded-xl shadow-sm p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Avatar Section */}
                <div className="flex items-center space-x-6">
                  <div className="relative w-24 h-24">
                    {profile.image ? (
                      <Image
                        src={profile.image}
                        alt={profile.name || 'User'}
                        fill
                        className="rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-24 h-24 bg-indigo-100 rounded-full flex items-center justify-center text-indigo-600 text-3xl font-bold">
                        {profile.name?.charAt(0) || 'U'}
                      </div>
                    )}
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Profile Picture</h3>
                    <AvatarUpload onUpload={handleAvatarUpdate} />
                  </div>
                </div>

                {/* Basic Information */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                        <UserIcon className="w-4 h-4 inline mr-2" />
                        Full Name
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="Enter your full name"
                      />
                    </div>

                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                        <EnvelopeIcon className="w-4 h-4 inline mr-2" />
                        Email Address
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="Enter your email"
                      />
                    </div>

                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                        <PhoneIcon className="w-4 h-4 inline mr-2" />
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="Enter your phone number"
                      />
                    </div>

                    <div>
                      <label htmlFor="dateOfBirth" className="block text-sm font-medium text-gray-700 mb-2">
                        <CalendarIcon className="w-4 h-4 inline mr-2" />
                        Date of Birth
                      </label>
                      <input
                        type="date"
                        id="dateOfBirth"
                        name="dateOfBirth"
                        value={formData.dateOfBirth}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                      />
                    </div>
                  </div>
                </div>

                {/* Additional Information */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Additional Information</h3>
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="bio" className="block text-sm font-medium text-gray-700 mb-2">
                        <DocumentTextIcon className="w-4 h-4 inline mr-2" />
                        Bio
                      </label>
                      <textarea
                        id="bio"
                        name="bio"
                        value={formData.bio}
                        onChange={handleInputChange}
                        rows={4}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none"
                        placeholder="Tell us about yourself..."
                      />
                    </div>

                    <div>
                      <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-2">
                        <MapPinIcon className="w-4 h-4 inline mr-2" />
                        Address
                      </label>
                      <input
                        type="text"
                        id="address"
                        name="address"
                        value={formData.address}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="Enter your address"
                      />
                    </div>
                  </div>
                </div>

                {/* Submit Button */}
                <div className="flex justify-end pt-6 border-t border-gray-200">
                  <button
                    type="submit"
                    disabled={isSaving}
                    className="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
                  >
                    {isSaving ? 'Saving...' : 'Save Changes'}
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* Verification Tab */}
          {activeTab === 'verification' && profile && (
            <div className="bg-white rounded-xl shadow-sm p-6">
              {verificationStep === 'overview' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Account Verification</h3>
                    <p className="text-gray-600 mb-6">
                      Verify your account to build trust with other users and unlock additional features.
                    </p>
                  </div>

                  {/* Verification Status */}
                  <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg">
                    {profile.isVerified ? (
                      <>
                        <CheckCircleIcon className="w-6 h-6 text-green-500" />
                        <div>
                          <p className="font-medium text-green-800">Account Verified</p>
                          <p className="text-sm text-green-600">Your account has been verified</p>
                        </div>
                      </>
                    ) : (
                      <>
                        <XCircleIcon className="w-6 h-6 text-yellow-500" />
                        <div>
                          <p className="font-medium text-yellow-800">Account Not Verified</p>
                          <p className="text-sm text-yellow-600">Complete verification to build trust</p>
                        </div>
                      </>
                    )}
                  </div>

                  {/* Verification Steps */}
                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">Verification Steps:</h4>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg">
                        <ShieldCheckIcon className="w-5 h-5 text-green-500" />
                        <div className="flex-1">
                          <p className="font-medium">Email Verification</p>
                          <p className="text-sm text-gray-600">Verify your email address</p>
                        </div>
                        <span className="text-sm text-green-600 font-medium">Completed</span>
                      </div>

                      <div className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg">
                        <PhoneIcon className={`w-5 h-5 ${profile.phone ? 'text-green-500' : 'text-gray-400'}`} />
                        <div className="flex-1">
                          <p className="font-medium">Phone Verification</p>
                          <p className="text-sm text-gray-600">Verify your phone number</p>
                        </div>
                        {profile.phone ? (
                          <span className="text-sm text-green-600 font-medium">Completed</span>
                        ) : (
                          <button
                            onClick={() => setVerificationStep('phone')}
                            className="text-sm text-indigo-600 font-medium hover:text-indigo-700"
                          >
                            Start
                          </button>
                        )}
                      </div>

                      <div className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg">
                        <DocumentTextIcon className={`w-5 h-5 ${
                          idVerificationStatus?.status === 'APPROVED' ? 'text-green-500' :
                          idVerificationStatus?.status === 'PENDING' ? 'text-yellow-500' :
                          'text-gray-400'
                        }`} />
                        <div className="flex-1">
                          <p className="font-medium">Identity Verification</p>
                          <p className="text-sm text-gray-600">Upload government-issued ID</p>
                        </div>
                        {idVerificationStatus?.status === 'APPROVED' ? (
                          <span className="text-sm text-green-600 font-medium">Verified</span>
                        ) : idVerificationStatus?.status === 'PENDING' ? (
                          <span className="text-sm text-yellow-600 font-medium">Under Review</span>
                        ) : (
                          <button
                            onClick={() => setVerificationStep('id')}
                            className="text-sm text-indigo-600 font-medium hover:text-indigo-700"
                          >
                            Start
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {verificationStep === 'phone' && (
                <div className="space-y-6">
                  <div className="flex items-center space-x-4 mb-6">
                    <button
                      onClick={() => setVerificationStep('overview')}
                      className="text-indigo-600 hover:text-indigo-700"
                    >
                      ← Back
                    </button>
                    <h3 className="text-lg font-medium text-gray-900">Phone Verification</h3>
                  </div>
                  <PhoneVerification onVerified={handlePhoneVerified} />
                </div>
              )}

              {verificationStep === 'id' && (
                <div className="space-y-6">
                  <div className="flex items-center space-x-4 mb-6">
                    <button
                      onClick={() => setVerificationStep('overview')}
                      className="text-indigo-600 hover:text-indigo-700"
                    >
                      ← Back
                    </button>
                    <h3 className="text-lg font-medium text-gray-900">Identity Verification</h3>
                  </div>
                  <IdVerification onSubmitted={handleIdSubmitted} />
                </div>
              )}
            </div>
          )}
        </div>
      </main>

      <Footer categories={categories} />
    </div>
  );
}

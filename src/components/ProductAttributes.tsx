'use client';

import React from 'react';
import {
  HomeIcon,
  TruckIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  ClockIcon,
  CogIcon,
  MapPinIcon,
  CurrencyDollarIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';

interface ProductAttributesProps {
  attributes: Record<string, any>;
  categorySlug: string;
  categoryName: string;
}

const ProductAttributes: React.FC<ProductAttributesProps> = ({
  attributes,
  categorySlug,
  categoryName
}) => {
  if (!attributes || Object.keys(attributes).length === 0) {
    return null;
  }

  const renderAttributeValue = (key: string, value: any) => {
    if (typeof value === 'boolean') {
      return (
        <div className="flex items-center">
          {value ? (
            <CheckCircleIcon className="h-4 w-4 text-green-500 mr-1" />
          ) : (
            <XCircleIcon className="h-4 w-4 text-red-500 mr-1" />
          )}
          <span>{value ? 'Yes' : 'No'}</span>
        </div>
      );
    }

    if (typeof value === 'number') {
      // Format numbers with commas for readability
      return value.toLocaleString();
    }

    return value?.toString() || 'Not specified';
  };

  const getAttributeIcon = (key: string, categorySlug: string) => {
    const iconClass = "h-4 w-4 text-gray-500";

    // Real Estate icons
    if (categorySlug.includes('real-estate') || categorySlug.includes('houses') || categorySlug.includes('apartments')) {
      switch (key.toLowerCase()) {
        case 'bedrooms':
        case 'bathrooms':
          return <HomeIcon className={iconClass} />;
        case 'squarefeet':
        case 'yearbuilt':
          return <CogIcon className={iconClass} />;
        case 'parking':
        case 'pool':
        case 'garden':
          return <CheckCircleIcon className={iconClass} />;
        default:
          return <HomeIcon className={iconClass} />;
      }
    }

    // Vehicle icons
    if (categorySlug.includes('vehicles') || categorySlug.includes('cars')) {
      switch (key.toLowerCase()) {
        case 'make':
        case 'model':
        case 'year':
          return <TruckIcon className={iconClass} />;
        case 'mileage':
        case 'enginesize':
          return <CogIcon className={iconClass} />;
        case 'fueltype':
        case 'transmission':
          return <CogIcon className={iconClass} />;
        default:
          return <TruckIcon className={iconClass} />;
      }
    }

    // Gadget icons
    if (categorySlug.includes('gadgets') || categorySlug.includes('smartphones') || categorySlug.includes('laptops')) {
      switch (key.toLowerCase()) {
        case 'brand':
        case 'model':
          return <DevicePhoneMobileIcon className={iconClass} />;
        case 'processor':
        case 'ram':
        case 'storage':
          return <ComputerDesktopIcon className={iconClass} />;
        case 'warranty':
          return <CheckCircleIcon className={iconClass} />;
        default:
          return <DevicePhoneMobileIcon className={iconClass} />;
      }
    }

    return <CogIcon className={iconClass} />;
  };

  const formatAttributeName = (key: string) => {
    // Convert camelCase to Title Case
    return key
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .replace(/\b\w/g, l => l.toUpperCase());
  };

  const groupAttributes = (attributes: Record<string, any>, categorySlug: string) => {
    const groups: Record<string, Record<string, any>> = {};

    if (categorySlug.includes('real-estate') || categorySlug.includes('houses') || categorySlug.includes('apartments')) {
      groups['Property Details'] = {};
      groups['Features & Amenities'] = {};

      Object.entries(attributes).forEach(([key, value]) => {
        if (['bedrooms', 'bathrooms', 'squareFeet', 'yearBuilt', 'propertyType'].includes(key)) {
          groups['Property Details'][key] = value;
        } else {
          groups['Features & Amenities'][key] = value;
        }
      });
    } else if (categorySlug.includes('vehicles') || categorySlug.includes('cars')) {
      groups['Vehicle Information'] = {};
      groups['Performance & Features'] = {};

      Object.entries(attributes).forEach(([key, value]) => {
        if (['make', 'model', 'year', 'color', 'doors'].includes(key)) {
          groups['Vehicle Information'][key] = value;
        } else {
          groups['Performance & Features'][key] = value;
        }
      });
    } else if (categorySlug.includes('gadgets') || categorySlug.includes('smartphones') || categorySlug.includes('laptops')) {
      groups['Device Information'] = {};
      groups['Technical Specifications'] = {};

      Object.entries(attributes).forEach(([key, value]) => {
        if (['brand', 'model', 'color', 'warranty', 'unlocked'].includes(key)) {
          groups['Device Information'][key] = value;
        } else {
          groups['Technical Specifications'][key] = value;
        }
      });
    } else {
      groups['Product Details'] = attributes;
    }

    // Remove empty groups
    Object.keys(groups).forEach(groupName => {
      if (Object.keys(groups[groupName]).length === 0) {
        delete groups[groupName];
      }
    });

    return groups;
  };

  const attributeGroups = groupAttributes(attributes, categorySlug);

  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold text-gray-900">Product Specifications</h3>

      {Object.entries(attributeGroups).map(([groupName, groupAttributes]) => (
        <div key={groupName} className="bg-gray-50 rounded-lg p-4">
          <h4 className="text-lg font-medium text-gray-800 mb-3">{groupName}</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {Object.entries(groupAttributes).map(([key, value]) => (
              <div key={key} className="flex items-center justify-between p-3 bg-white rounded-lg">
                <div className="flex items-center space-x-2">
                  {getAttributeIcon(key, categorySlug)}
                  <span className="text-sm font-medium text-gray-700">
                    {formatAttributeName(key)}
                  </span>
                </div>
                <div className="text-sm text-gray-900 font-medium">
                  {renderAttributeValue(key, value)}
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default ProductAttributes;

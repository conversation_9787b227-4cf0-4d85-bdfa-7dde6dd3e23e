'use client';

import React from 'react';
import { 
  ExclamationTriangleIcon,
  ShieldCheckIcon,
  EyeIcon,
  DocumentCheckIcon,
  MapPinIcon,
  CurrencyDollarIcon,
  PhoneIcon,
  UserGroupIcon
} from '@heroicons/react/24/outline';

interface CategorySafetyTipsProps {
  categorySlug: string;
  categoryName: string;
}

const CategorySafetyTips: React.FC<CategorySafetyTipsProps> = ({ 
  categorySlug, 
  categoryName 
}) => {
  const getRealEstateTips = () => [
    {
      icon: <DocumentCheckIcon className="h-5 w-5" />,
      title: "Verify Property Documents",
      description: "Always request and verify property titles, permits, and legal documents before making any payments."
    },
    {
      icon: <EyeIcon className="h-5 w-5" />,
      title: "Physical Inspection",
      description: "Schedule a thorough inspection of the property. Check for structural issues, utilities, and neighborhood safety."
    },
    {
      icon: <MapPinIcon className="h-5 w-5" />,
      title: "Location Verification",
      description: "Verify the exact location and ensure it matches the description. Check local amenities and accessibility."
    },
    {
      icon: <CurrencyDollarIcon className="h-5 w-5" />,
      title: "Secure Payment Methods",
      description: "Use escrow services for large transactions. Never wire money or pay large sums without proper documentation."
    }
  ];

  const getVehicleTips = () => [
    {
      icon: <DocumentCheckIcon className="h-5 w-5" />,
      title: "Check Vehicle History",
      description: "Request maintenance records, accident history, and verify the VIN number. Check for liens or outstanding loans."
    },
    {
      icon: <EyeIcon className="h-5 w-5" />,
      title: "Professional Inspection",
      description: "Have a qualified mechanic inspect the vehicle. Test drive in various conditions and check all systems."
    },
    {
      icon: <ShieldCheckIcon className="h-5 w-5" />,
      title: "Verify Ownership",
      description: "Ensure the seller is the legal owner. Check that the title is clear and matches the seller's identification."
    },
    {
      icon: <CurrencyDollarIcon className="h-5 w-5" />,
      title: "Safe Payment",
      description: "Meet at a bank for payment. Use cashier's checks or bank transfers. Avoid cash for high-value transactions."
    }
  ];

  const getGadgetTips = () => [
    {
      icon: <ShieldCheckIcon className="h-5 w-5" />,
      title: "Verify Authenticity",
      description: "Check serial numbers and authenticity. Be wary of deals that seem too good to be true."
    },
    {
      icon: <EyeIcon className="h-5 w-5" />,
      title: "Test Before Buying",
      description: "Test all functions, check for damage, and verify that the device is unlocked if applicable."
    },
    {
      icon: <DocumentCheckIcon className="h-5 w-5" />,
      title: "Check Warranty Status",
      description: "Verify warranty coverage and transferability. Keep all original accessories and documentation."
    },
    {
      icon: <UserGroupIcon className="h-5 w-5" />,
      title: "Meet in Public",
      description: "Meet in well-lit public places, preferably at electronics stores or carrier locations for verification."
    }
  ];

  const getGeneralTips = () => [
    {
      icon: <UserGroupIcon className="h-5 w-5" />,
      title: "Meet in Public Places",
      description: "Always meet in safe, public locations. Consider meeting at police stations with designated safe exchange zones."
    },
    {
      icon: <PhoneIcon className="h-5 w-5" />,
      title: "Verify Seller Identity",
      description: "Communicate through the platform. Verify the seller's identity and check their ratings and reviews."
    },
    {
      icon: <CurrencyDollarIcon className="h-5 w-5" />,
      title: "Secure Payment Methods",
      description: "Use secure payment methods. Avoid wire transfers or prepayments to unknown sellers."
    },
    {
      icon: <ExclamationTriangleIcon className="h-5 w-5" />,
      title: "Trust Your Instincts",
      description: "If something feels wrong, trust your instincts. Don't proceed with transactions that make you uncomfortable."
    }
  ];

  const getTipsForCategory = () => {
    if (categorySlug.includes('real-estate') || categorySlug.includes('houses') || categorySlug.includes('apartments')) {
      return getRealEstateTips();
    } else if (categorySlug.includes('vehicles') || categorySlug.includes('cars')) {
      return getVehicleTips();
    } else if (categorySlug.includes('gadgets') || categorySlug.includes('smartphones') || categorySlug.includes('laptops')) {
      return getGadgetTips();
    } else {
      return getGeneralTips();
    }
  };

  const tips = getTipsForCategory();

  return (
    <div className="bg-amber-50 border border-amber-200 rounded-lg p-6">
      <div className="flex items-center mb-4">
        <ExclamationTriangleIcon className="h-6 w-6 text-amber-600 mr-2" />
        <h3 className="text-lg font-semibold text-amber-800">
          Safety Tips for {categoryName}
        </h3>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {tips.map((tip, index) => (
          <div key={index} className="flex items-start space-x-3 p-3 bg-white rounded-lg border border-amber-100">
            <div className="flex-shrink-0 text-amber-600 mt-0.5">
              {tip.icon}
            </div>
            <div>
              <h4 className="font-medium text-amber-900 mb-1">{tip.title}</h4>
              <p className="text-sm text-amber-700">{tip.description}</p>
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-4 p-3 bg-amber-100 rounded-lg">
        <p className="text-sm text-amber-800">
          <strong>Remember:</strong> Always prioritize your safety and security. When in doubt, 
          seek advice from professionals or trusted friends and family members.
        </p>
      </div>
    </div>
  );
};

export default CategorySafetyTips;

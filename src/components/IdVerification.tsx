'use client';

import { useState } from 'react';
import { DocumentTextIcon, CloudArrowUpIcon } from '@heroicons/react/24/outline';

interface IdVerificationProps {
  onSubmitted: () => void;
}

export default function IdVerification({ onSubmitted }: IdVerificationProps) {
  const [documentType, setDocumentType] = useState('');
  const [frontImage, setFrontImage] = useState<File | null>(null);
  const [backImage, setBackImage] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const documentTypes = [
    { value: 'passport', label: 'Passport' },
    { value: 'drivers_license', label: "Driver's License" },
    { value: 'national_id', label: 'National ID Card' },
    { value: 'state_id', label: 'State ID Card' },
  ];

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, side: 'front' | 'back') => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!allowedTypes.includes(file.type)) {
      setError('Please select a valid image file (JPEG or PNG)');
      return;
    }

    // Validate file size (10MB max)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      setError('File size must be less than 10MB');
      return;
    }

    setError(null);
    if (side === 'front') {
      setFrontImage(file);
    } else {
      setBackImage(file);
    }
  };

  const uploadFile = async (file: File): Promise<string> => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to upload file');
    }

    const { url } = await response.json();
    return url;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!documentType) {
      setError('Please select a document type');
      return;
    }

    if (!frontImage) {
      setError('Please upload the front of your document');
      return;
    }

    if (documentType !== 'passport' && !backImage) {
      setError('Please upload the back of your document');
      return;
    }

    setIsUploading(true);

    try {
      // Upload images
      const frontUrl = await uploadFile(frontImage);
      const backUrl = backImage ? await uploadFile(backImage) : null;

      // Submit verification request
      const response = await fetch('/api/verification/id', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          documentType,
          frontImageUrl: frontUrl,
          backImageUrl: backUrl,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to submit verification');
      }

      onSubmitted();
    } catch (err: any) {
      setError(err.message || 'Failed to submit verification');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Document Type Selection */}
        <div>
          <label htmlFor="documentType" className="block text-sm font-medium text-gray-700 mb-2">
            <DocumentTextIcon className="w-4 h-4 inline mr-2" />
            Document Type
          </label>
          <select
            id="documentType"
            value={documentType}
            onChange={(e) => setDocumentType(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            required
          >
            <option value="">Select document type</option>
            {documentTypes.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>

        {/* Front Image Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <CloudArrowUpIcon className="w-4 h-4 inline mr-2" />
            Front of Document
          </label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-indigo-400 transition-colors">
            <input
              type="file"
              accept="image/*"
              onChange={(e) => handleFileChange(e, 'front')}
              className="hidden"
              id="front-upload"
            />
            <label htmlFor="front-upload" className="cursor-pointer">
              {frontImage ? (
                <div>
                  <p className="text-sm font-medium text-gray-900">{frontImage.name}</p>
                  <p className="text-xs text-gray-500">Click to change</p>
                </div>
              ) : (
                <div>
                  <CloudArrowUpIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-sm font-medium text-gray-900 mb-2">Upload front of document</p>
                  <p className="text-xs text-gray-500">JPEG or PNG, max 10MB</p>
                </div>
              )}
            </label>
          </div>
        </div>

        {/* Back Image Upload (if not passport) */}
        {documentType && documentType !== 'passport' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <CloudArrowUpIcon className="w-4 h-4 inline mr-2" />
              Back of Document
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-indigo-400 transition-colors">
              <input
                type="file"
                accept="image/*"
                onChange={(e) => handleFileChange(e, 'back')}
                className="hidden"
                id="back-upload"
              />
              <label htmlFor="back-upload" className="cursor-pointer">
                {backImage ? (
                  <div>
                    <p className="text-sm font-medium text-gray-900">{backImage.name}</p>
                    <p className="text-xs text-gray-500">Click to change</p>
                  </div>
                ) : (
                  <div>
                    <CloudArrowUpIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-sm font-medium text-gray-900 mb-2">Upload back of document</p>
                    <p className="text-xs text-gray-500">JPEG or PNG, max 10MB</p>
                  </div>
                )}
              </label>
            </div>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="text-sm text-red-600 bg-red-50 border border-red-200 rounded-lg p-3">
            {error}
          </div>
        )}

        {/* Important Notes */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-900 mb-2">Important Notes:</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Ensure all text is clearly visible and readable</li>
            <li>• Document should be valid and not expired</li>
            <li>• Images should be well-lit and in focus</li>
            <li>• Verification typically takes 1-3 business days</li>
          </ul>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isUploading || !documentType || !frontImage || (documentType !== 'passport' && !backImage)}
          className="w-full px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
        >
          {isUploading ? 'Uploading...' : 'Submit for Verification'}
        </button>
      </form>
    </div>
  );
}

'use client';

import Link from 'next/link';
import { Category } from '@/types';
import { FacebookIcon, TwitterIcon, InstagramIcon } from './Icons';

interface FooterProps {
  categories?: Category[];
}

export default function Footer({ categories = [] }: FooterProps) {
  return (
    <footer className="bg-white border-t border-slate-100">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-lg font-semibold mb-4">Categories</h3>
            <ul className="space-y-2">
              {categories && categories.length > 0 ? categories.map((category) => (
                <li key={category.id}>
                  <Link
                    href={`/categories/${category.slug}`}
                    className="text-slate-600 hover:text-slate-900"
                  >
                    {category.name}
                  </Link>
                </li>
              )) : (
                <li className="text-slate-600">No categories available</li>
              )}
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/products" className="text-slate-600 hover:text-slate-900">
                  All Products
                </Link>
              </li>
              <li>
                <Link href="/sellers" className="text-slate-600 hover:text-slate-900">
                  Top Sellers
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-slate-600 hover:text-slate-900">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-slate-600 hover:text-slate-900">
                  Contact
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">Help & Support</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/faqs" className="text-slate-600 hover:text-slate-900">
                  FAQ
                </Link>
              </li>
              <li>
                <Link href="/shipping" className="text-slate-600 hover:text-slate-900">
                  Shipping Info
                </Link>
              </li>
              <li>
                <Link href="/returns" className="text-slate-600 hover:text-slate-900">
                  Returns
                </Link>
              </li>
              <li>
                <Link href="/privacy-policy" className="text-slate-600 hover:text-slate-900">
                  Privacy Policy
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">Newsletter</h3>
            <p className="text-slate-600 mb-4">
              Subscribe to get special offers, free giveaways, and once-in-a-lifetime deals.
            </p>
            <form className="flex gap-2">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-2 rounded-lg border border-slate-200 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
              <button
                type="submit"
                className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition"
              >
                Subscribe
              </button>
            </form>
          </div>
        </div>
        <div className="mt-12 pt-8 border-t border-slate-100 text-center text-slate-600">
          <p>&copy; {new Date().getFullYear()} Marketplace. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}
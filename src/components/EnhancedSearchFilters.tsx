'use client';

import React, { useState, useEffect } from 'react';
import { 
  FunnelIcon,
  XMarkIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';

interface Category {
  id: string;
  name: string;
  slug: string;
  icon: string;
}

interface CategoryAttribute {
  id: string;
  name: string;
  type: string;
  options: string[];
  isRequired: boolean;
}

interface SearchFilters {
  search: string;
  category: string;
  condition: string;
  minPrice: string;
  maxPrice: string;
  location: string;
  attributes: Record<string, any>;
}

interface EnhancedSearchFiltersProps {
  onFilterChange: (filters: SearchFilters) => void;
  initialFilters?: Partial<SearchFilters>;
  showSearch?: boolean;
  layout?: 'horizontal' | 'vertical';
}

const EnhancedSearchFilters: React.FC<EnhancedSearchFiltersProps> = ({
  onFilterChange,
  initialFilters = {},
  showSearch = true,
  layout = 'horizontal'
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [categoryAttributes, setCategoryAttributes] = useState<CategoryAttribute[]>([]);
  
  const [filters, setFilters] = useState<SearchFilters>({
    search: '',
    category: '',
    condition: '',
    minPrice: '',
    maxPrice: '',
    location: '',
    attributes: {},
    ...initialFilters
  });

  // Fetch categories on component mount
  useEffect(() => {
    fetchCategories();
  }, []);

  // Fetch category attributes when category changes
  useEffect(() => {
    if (filters.category) {
      fetchCategoryAttributes(filters.category);
    } else {
      setCategoryAttributes([]);
    }
  }, [filters.category]);

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      if (response.ok) {
        const data = await response.json();
        setCategories(data);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchCategoryAttributes = async (categorySlug: string) => {
    try {
      const response = await fetch(`/api/categories/${categorySlug}/attributes`);
      if (response.ok) {
        const data = await response.json();
        setCategoryAttributes(data);
      }
    } catch (error) {
      console.error('Error fetching category attributes:', error);
      // Fallback to hardcoded attributes for demo
      setHardcodedAttributes(categorySlug);
    }
  };

  const setHardcodedAttributes = (categorySlug: string) => {
    if (categorySlug.includes('real-estate') || categorySlug.includes('houses') || categorySlug.includes('apartments')) {
      setCategoryAttributes([
        { id: 'bedrooms', name: 'Bedrooms', type: 'number', options: [], isRequired: false },
        { id: 'bathrooms', name: 'Bathrooms', type: 'number', options: [], isRequired: false },
        { id: 'propertyType', name: 'Property Type', type: 'select', options: ['Single Family', 'Duplex', 'Apartment', 'Townhouse'], isRequired: false },
        { id: 'furnished', name: 'Furnished', type: 'boolean', options: [], isRequired: false },
      ]);
    } else if (categorySlug.includes('vehicles') || categorySlug.includes('cars')) {
      setCategoryAttributes([
        { id: 'make', name: 'Make', type: 'text', options: [], isRequired: false },
        { id: 'year', name: 'Year', type: 'number', options: [], isRequired: false },
        { id: 'fuelType', name: 'Fuel Type', type: 'select', options: ['Gasoline', 'Diesel', 'Electric', 'Hybrid'], isRequired: false },
        { id: 'transmission', name: 'Transmission', type: 'select', options: ['Automatic', 'Manual'], isRequired: false },
      ]);
    } else if (categorySlug.includes('gadgets') || categorySlug.includes('smartphones') || categorySlug.includes('laptops')) {
      setCategoryAttributes([
        { id: 'brand', name: 'Brand', type: 'text', options: [], isRequired: false },
        { id: 'storage', name: 'Storage', type: 'select', options: ['64GB', '128GB', '256GB', '512GB', '1TB'], isRequired: false },
        { id: 'warranty', name: 'Warranty', type: 'boolean', options: [], isRequired: false },
      ]);
    }
  };

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    
    // Reset attributes when category changes
    if (key === 'category') {
      newFilters.attributes = {};
    }
    
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const handleAttributeChange = (attributeId: string, value: any) => {
    const newAttributes = { ...filters.attributes, [attributeId]: value };
    const newFilters = { ...filters, attributes: newAttributes };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const clearFilters = () => {
    const clearedFilters: SearchFilters = {
      search: '',
      category: '',
      condition: '',
      minPrice: '',
      maxPrice: '',
      location: '',
      attributes: {}
    };
    setFilters(clearedFilters);
    onFilterChange(clearedFilters);
  };

  const renderAttributeField = (attribute: CategoryAttribute) => {
    const value = filters.attributes[attribute.id] || '';

    switch (attribute.type) {
      case 'select':
        return (
          <div key={attribute.id} className="space-y-1">
            <label className="block text-sm font-medium text-gray-700">
              {attribute.name}
            </label>
            <select
              value={value}
              onChange={(e) => handleAttributeChange(attribute.id, e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              <option value="">Any {attribute.name}</option>
              {attribute.options.map(option => (
                <option key={option} value={option}>{option}</option>
              ))}
            </select>
          </div>
        );

      case 'number':
        return (
          <div key={attribute.id} className="space-y-1">
            <label className="block text-sm font-medium text-gray-700">
              {attribute.name}
            </label>
            <input
              type="number"
              value={value}
              onChange={(e) => handleAttributeChange(attribute.id, e.target.value)}
              placeholder={`Enter ${attribute.name.toLowerCase()}`}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
        );

      case 'boolean':
        return (
          <div key={attribute.id} className="space-y-1">
            <label className="block text-sm font-medium text-gray-700">
              {attribute.name}
            </label>
            <select
              value={value}
              onChange={(e) => handleAttributeChange(attribute.id, e.target.value === 'true')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              <option value="">Any</option>
              <option value="true">Yes</option>
              <option value="false">No</option>
            </select>
          </div>
        );

      case 'text':
      default:
        return (
          <div key={attribute.id} className="space-y-1">
            <label className="block text-sm font-medium text-gray-700">
              {attribute.name}
            </label>
            <input
              type="text"
              value={value}
              onChange={(e) => handleAttributeChange(attribute.id, e.target.value)}
              placeholder={`Enter ${attribute.name.toLowerCase()}`}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
        );
    }
  };

  const activeFiltersCount = Object.values(filters).filter(value => 
    value !== '' && value !== null && value !== undefined && 
    (typeof value !== 'object' || Object.keys(value).length > 0)
  ).length;

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <FunnelIcon className="h-5 w-5 text-gray-500" />
          <span className="font-medium text-gray-900">Filters</span>
          {activeFiltersCount > 0 && (
            <span className="bg-indigo-100 text-indigo-800 text-xs font-medium px-2 py-1 rounded-full">
              {activeFiltersCount}
            </span>
          )}
        </div>
        <div className="flex items-center space-x-2">
          {activeFiltersCount > 0 && (
            <button
              onClick={clearFilters}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              Clear all
            </button>
          )}
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-1 text-gray-500 hover:text-gray-700"
          >
            {isExpanded ? (
              <ChevronUpIcon className="h-4 w-4" />
            ) : (
              <ChevronDownIcon className="h-4 w-4" />
            )}
          </button>
        </div>
      </div>

      {/* Search Bar */}
      {showSearch && (
        <div className="p-4 border-b border-gray-200">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              placeholder="Search products..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
        </div>
      )}

      {/* Filters Content */}
      {isExpanded && (
        <div className="p-4 space-y-4">
          <div className={`grid gap-4 ${layout === 'horizontal' ? 'grid-cols-1 md:grid-cols-3 lg:grid-cols-5' : 'grid-cols-1'}`}>
            {/* Category Filter */}
            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">Category</label>
              <select
                value={filters.category}
                onChange={(e) => handleFilterChange('category', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="">All Categories</option>
                {categories.map(category => (
                  <option key={category.id} value={category.slug}>
                    {category.icon} {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Condition Filter */}
            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">Condition</label>
              <select
                value={filters.condition}
                onChange={(e) => handleFilterChange('condition', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="">All Conditions</option>
                <option value="NEW">New</option>
                <option value="USED">Used</option>
                <option value="REFURBISHED">Refurbished</option>
              </select>
            </div>

            {/* Price Range */}
            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">Min Price</label>
              <input
                type="number"
                value={filters.minPrice}
                onChange={(e) => handleFilterChange('minPrice', e.target.value)}
                placeholder="Min price"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
            </div>

            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">Max Price</label>
              <input
                type="number"
                value={filters.maxPrice}
                onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
                placeholder="Max price"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
            </div>

            {/* Location Filter */}
            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">Location</label>
              <input
                type="text"
                value={filters.location}
                onChange={(e) => handleFilterChange('location', e.target.value)}
                placeholder="City or state"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
            </div>
          </div>

          {/* Category-specific attributes */}
          {categoryAttributes.length > 0 && (
            <div className="border-t border-gray-200 pt-4">
              <h3 className="text-sm font-medium text-gray-900 mb-3">
                {filters.category ? `${categories.find(c => c.slug === filters.category)?.name || 'Category'} Specifications` : 'Specifications'}
              </h3>
              <div className={`grid gap-4 ${layout === 'horizontal' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4' : 'grid-cols-1'}`}>
                {categoryAttributes.map(renderAttributeField)}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default EnhancedSearchFilters;

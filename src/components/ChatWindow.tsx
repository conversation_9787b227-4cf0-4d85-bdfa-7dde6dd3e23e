'use client';

import { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import Image from 'next/image';
import { useSocket } from '@/hooks/useSocket';
import {
  PaperAirplaneIcon,
  PhotoIcon,
  PaperClipIcon,
  EllipsisVerticalIcon,
} from '@heroicons/react/24/outline';

interface Message {
  id: string;
  content: string;
  senderId: string;
  chatId: string;
  createdAt: string;
  read: boolean;
  sender: {
    id: string;
    name: string;
    image?: string;
  };
  attachments?: {
    id: string;
    type: string;
    url: string;
    name: string;
  }[];
}

interface Chat {
  id: string;
  product: {
    id: string;
    name: string;
    price: number;
    images: string[];
    status: string;
  };
  buyer: {
    id: string;
    name: string;
    image?: string;
  };
  seller: {
    id: string;
    name: string;
    image?: string;
  };
}

interface ChatWindowProps {
  chat: Chat;
  onClose?: () => void;
}

export default function ChatWindow({ chat, onClose }: ChatWindowProps) {
  const { data: session } = useSession();
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  const {
    isConnected,
    joinChat,
    leaveChat,
    sendMessage,
    startTyping,
    stopTyping,
    markMessagesAsRead,
    on,
    off,
  } = useSocket();

  const otherUser = chat.buyer.id === session?.user?.id ? chat.seller : chat.buyer;

  useEffect(() => {
    if (isConnected) {
      joinChat(chat.id);
      fetchMessages();
    }

    return () => {
      if (isConnected) {
        leaveChat(chat.id);
      }
    };
  }, [chat.id, isConnected]);

  useEffect(() => {
    const handleNewMessage = (message: Message) => {
      if (message.chatId === chat.id) {
        setMessages(prev => [...prev, message]);
        scrollToBottom();
        
        // Mark as read if not from current user
        if (message.senderId !== session?.user?.id) {
          markMessagesAsRead(chat.id);
        }
      }
    };

    const handleUserTyping = (data: { userId: string; userName: string }) => {
      if (data.userId !== session?.user?.id) {
        setTypingUsers(prev => [...prev.filter(id => id !== data.userId), data.userId]);
      }
    };

    const handleUserStoppedTyping = (data: { userId: string; userName: string }) => {
      setTypingUsers(prev => prev.filter(id => id !== data.userId));
    };

    on('new-message', handleNewMessage);
    on('user-typing', handleUserTyping);
    on('user-stopped-typing', handleUserStoppedTyping);

    return () => {
      off('new-message', handleNewMessage);
      off('user-typing', handleUserTyping);
      off('user-stopped-typing', handleUserStoppedTyping);
    };
  }, [chat.id, session?.user?.id, on, off, markMessagesAsRead]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const fetchMessages = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/chats/${chat.id}`);
      if (response.ok) {
        const data = await response.json();
        setMessages(data.messages);
      }
    } catch (error) {
      console.error('Error fetching messages:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newMessage.trim() || isSending) return;

    const messageContent = newMessage.trim();
    setNewMessage('');
    setIsSending(true);

    try {
      if (isConnected) {
        sendMessage(chat.id, messageContent);
      } else {
        // Fallback to HTTP API if socket not connected
        const response = await fetch(`/api/chats/${chat.id}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ content: messageContent }),
        });

        if (response.ok) {
          const message = await response.json();
          setMessages(prev => [...prev, message]);
        }
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setNewMessage(messageContent); // Restore message on error
    } finally {
      setIsSending(false);
    }
  };

  const handleTyping = () => {
    if (isConnected) {
      startTyping(chat.id);
      
      // Clear existing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      
      // Stop typing after 3 seconds of inactivity
      typingTimeoutRef.current = setTimeout(() => {
        stopTyping(chat.id);
      }, 3000);
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Chat Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="relative w-10 h-10">
            {otherUser.image ? (
              <Image
                src={otherUser.image}
                alt={otherUser.name}
                fill
                className="rounded-full object-cover"
              />
            ) : (
              <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center text-indigo-600 font-semibold">
                {otherUser.name.charAt(0)}
              </div>
            )}
            <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-400 border-2 border-white rounded-full"></div>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">{otherUser.name}</h3>
            <p className="text-sm text-gray-500">{chat.product.name}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className="text-lg font-bold text-indigo-600">
            ₦{chat.product.price.toLocaleString()}
          </span>
          <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg">
            <EllipsisVerticalIcon className="w-5 h-5" />
          </button>
          {onClose && (
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
            >
              ✕
            </button>
          )}
        </div>
      </div>

      {/* Product Info */}
      <div className="p-3 bg-gray-50 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          {chat.product.images.length > 0 && (
            <div className="relative w-12 h-12">
              <Image
                src={chat.product.images[0]}
                alt={chat.product.name}
                fill
                className="rounded-lg object-cover"
              />
            </div>
          )}
          <div className="flex-1">
            <p className="font-medium text-gray-900">{chat.product.name}</p>
            <p className="text-sm text-gray-500">₦{chat.product.price.toLocaleString()}</p>
          </div>
          <span className={`px-2 py-1 text-xs rounded-full ${
            chat.product.status === 'ACTIVE' 
              ? 'bg-green-100 text-green-800' 
              : 'bg-gray-100 text-gray-800'
          }`}>
            {chat.product.status}
          </span>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {isLoading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
          </div>
        ) : messages.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>No messages yet. Start the conversation!</p>
          </div>
        ) : (
          messages.map((message, index) => {
            const isOwnMessage = message.senderId === session?.user?.id;
            const showDate = index === 0 || 
              formatDate(messages[index - 1].createdAt) !== formatDate(message.createdAt);

            return (
              <div key={message.id}>
                {showDate && (
                  <div className="text-center py-2">
                    <span className="text-xs text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                      {formatDate(message.createdAt)}
                    </span>
                  </div>
                )}
                
                <div className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
                  <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    isOwnMessage 
                      ? 'bg-indigo-600 text-white' 
                      : 'bg-gray-100 text-gray-900'
                  }`}>
                    <p className="text-sm">{message.content}</p>
                    {message.attachments && message.attachments.length > 0 && (
                      <div className="mt-2 space-y-1">
                        {message.attachments.map((attachment) => (
                          <div key={attachment.id} className="text-xs">
                            <a 
                              href={attachment.url} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="underline"
                            >
                              {attachment.name}
                            </a>
                          </div>
                        ))}
                      </div>
                    )}
                    <p className={`text-xs mt-1 ${
                      isOwnMessage ? 'text-indigo-200' : 'text-gray-500'
                    }`}>
                      {formatTime(message.createdAt)}
                    </p>
                  </div>
                </div>
              </div>
            );
          })
        )}

        {/* Typing Indicator */}
        {typingUsers.length > 0 && (
          <div className="flex justify-start">
            <div className="bg-gray-100 text-gray-900 px-4 py-2 rounded-lg">
              <div className="flex items-center space-x-1">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span className="text-xs text-gray-500 ml-2">typing...</span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="p-4 border-t border-gray-200">
        <form onSubmit={handleSendMessage} className="flex items-center space-x-2">
          <button
            type="button"
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
          >
            <PaperClipIcon className="w-5 h-5" />
          </button>
          <button
            type="button"
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
          >
            <PhotoIcon className="w-5 h-5" />
          </button>
          <input
            type="text"
            value={newMessage}
            onChange={(e) => {
              setNewMessage(e.target.value);
              handleTyping();
            }}
            placeholder="Type a message..."
            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            disabled={isSending}
          />
          <button
            type="submit"
            disabled={!newMessage.trim() || isSending}
            className="p-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <PaperAirplaneIcon className="w-5 h-5" />
          </button>
        </form>
        
        {!isConnected && (
          <p className="text-xs text-yellow-600 mt-2">
            Real-time messaging unavailable. Messages will be sent when connection is restored.
          </p>
        )}
      </div>
    </div>
  );
}

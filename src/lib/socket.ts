import { Server as NetServer } from 'http';
import { NextApiRequest, NextApiResponse } from 'next';
import { Server as ServerIO } from 'socket.io';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { prisma } from '@/lib/prisma';

export type NextApiResponseServerIO = NextApiResponse & {
  socket: {
    server: NetServer & {
      io: ServerIO;
    };
  };
};

export const config = {
  api: {
    bodyParser: false,
  },
};

interface SocketUser {
  id: string;
  name: string;
  email: string;
}

interface ChatMessage {
  id: string;
  content: string;
  senderId: string;
  chatId: string;
  createdAt: Date;
  sender: {
    id: string;
    name: string;
    image?: string;
  };
}

export function initializeSocket(server: NetServer): ServerIO {
  if (!server.io) {
    console.log('Initializing Socket.IO server...');
    
    const io = new ServerIO(server, {
      path: '/api/socket',
      addTrailingSlash: false,
      cors: {
        origin: process.env.NODE_ENV === 'production' 
          ? process.env.NEXTAUTH_URL 
          : 'http://localhost:3000',
        methods: ['GET', 'POST'],
        credentials: true,
      },
    });

    // Authentication middleware
    io.use(async (socket, next) => {
      try {
        const session = await getServerSession(authOptions);
        
        if (!session?.user) {
          return next(new Error('Authentication error'));
        }

        socket.data.user = session.user;
        next();
      } catch (error) {
        console.error('Socket authentication error:', error);
        next(new Error('Authentication error'));
      }
    });

    io.on('connection', (socket) => {
      const user = socket.data.user as SocketUser;
      console.log(`User ${user.name} connected with socket ${socket.id}`);

      // Join user to their personal room
      socket.join(`user:${user.id}`);

      // Handle joining chat rooms
      socket.on('join-chat', async (chatId: string) => {
        try {
          // Verify user has access to this chat
          const chat = await prisma.chat.findFirst({
            where: {
              id: chatId,
              OR: [
                { buyerId: user.id },
                { sellerId: user.id },
              ],
            },
          });

          if (!chat) {
            socket.emit('error', { message: 'Chat not found or access denied' });
            return;
          }

          socket.join(`chat:${chatId}`);
          console.log(`User ${user.name} joined chat ${chatId}`);
          
          // Notify other participants that user joined
          socket.to(`chat:${chatId}`).emit('user-joined', {
            userId: user.id,
            userName: user.name,
          });
        } catch (error) {
          console.error('Error joining chat:', error);
          socket.emit('error', { message: 'Failed to join chat' });
        }
      });

      // Handle leaving chat rooms
      socket.on('leave-chat', (chatId: string) => {
        socket.leave(`chat:${chatId}`);
        console.log(`User ${user.name} left chat ${chatId}`);
        
        // Notify other participants that user left
        socket.to(`chat:${chatId}`).emit('user-left', {
          userId: user.id,
          userName: user.name,
        });
      });

      // Handle sending messages
      socket.on('send-message', async (data: {
        chatId: string;
        content: string;
        attachments?: { type: string; url: string; name: string }[];
      }) => {
        try {
          const { chatId, content, attachments } = data;

          // Verify user has access to this chat
          const chat = await prisma.chat.findFirst({
            where: {
              id: chatId,
              OR: [
                { buyerId: user.id },
                { sellerId: user.id },
              ],
            },
          });

          if (!chat) {
            socket.emit('error', { message: 'Chat not found or access denied' });
            return;
          }

          // Create message in database
          const message = await prisma.message.create({
            data: {
              content,
              chatId,
              senderId: user.id,
              attachments: attachments ? {
                create: attachments.map(att => ({
                  type: att.type,
                  url: att.url,
                  name: att.name,
                  size: 0, // You might want to track file sizes
                })),
              } : undefined,
            },
            include: {
              sender: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
              attachments: true,
            },
          });

          // Update chat's updatedAt timestamp
          await prisma.chat.update({
            where: { id: chatId },
            data: { updatedAt: new Date() },
          });

          // Emit message to all participants in the chat
          io.to(`chat:${chatId}`).emit('new-message', message);

          // Send push notification to offline users (if implemented)
          const otherParticipantId = chat.buyerId === user.id ? chat.sellerId : chat.buyerId;
          socket.to(`user:${otherParticipantId}`).emit('notification', {
            type: 'new-message',
            chatId,
            message: `${user.name}: ${content}`,
            senderId: user.id,
          });

        } catch (error) {
          console.error('Error sending message:', error);
          socket.emit('error', { message: 'Failed to send message' });
        }
      });

      // Handle typing indicators
      socket.on('typing-start', (chatId: string) => {
        socket.to(`chat:${chatId}`).emit('user-typing', {
          userId: user.id,
          userName: user.name,
        });
      });

      socket.on('typing-stop', (chatId: string) => {
        socket.to(`chat:${chatId}`).emit('user-stopped-typing', {
          userId: user.id,
          userName: user.name,
        });
      });

      // Handle marking messages as read
      socket.on('mark-messages-read', async (chatId: string) => {
        try {
          await prisma.message.updateMany({
            where: {
              chatId,
              senderId: { not: user.id },
              read: false,
            },
            data: { read: true },
          });

          // Notify other participants that messages were read
          socket.to(`chat:${chatId}`).emit('messages-read', {
            userId: user.id,
            chatId,
          });
        } catch (error) {
          console.error('Error marking messages as read:', error);
        }
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        console.log(`User ${user.name} disconnected`);
      });
    });

    server.io = io;
  }

  return server.io;
}

export default initializeSocket;

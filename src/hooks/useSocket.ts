'use client';

import { useEffect, useRef, useState } from 'react';
import { useSession } from 'next-auth/react';
import { io, Socket } from 'socket.io-client';

interface Message {
  id: string;
  content: string;
  senderId: string;
  chatId: string;
  createdAt: string;
  read: boolean;
  sender: {
    id: string;
    name: string;
    image?: string;
  };
  attachments?: {
    id: string;
    type: string;
    url: string;
    name: string;
  }[];
}

interface SocketEvents {
  'new-message': (message: Message) => void;
  'user-joined': (data: { userId: string; userName: string }) => void;
  'user-left': (data: { userId: string; userName: string }) => void;
  'user-typing': (data: { userId: string; userName: string }) => void;
  'user-stopped-typing': (data: { userId: string; userName: string }) => void;
  'messages-read': (data: { userId: string; chatId: string }) => void;
  'notification': (data: { type: string; chatId: string; message: string; senderId: string }) => void;
  'error': (data: { message: string }) => void;
}

export function useSocket() {
  const { data: session, status } = useSession();
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const socketRef = useRef<Socket | null>(null);

  useEffect(() => {
    if (status === 'loading') return;
    
    if (session?.user && !socketRef.current) {
      console.log('Initializing socket connection...');
      
      const newSocket = io(process.env.NODE_ENV === 'production' 
        ? process.env.NEXTAUTH_URL || '' 
        : 'http://localhost:3000', {
        path: '/api/socket',
        transports: ['websocket', 'polling'],
        timeout: 20000,
        forceNew: true,
      });

      newSocket.on('connect', () => {
        console.log('Socket connected:', newSocket.id);
        setIsConnected(true);
        setConnectionError(null);
      });

      newSocket.on('disconnect', (reason) => {
        console.log('Socket disconnected:', reason);
        setIsConnected(false);
      });

      newSocket.on('connect_error', (error) => {
        console.error('Socket connection error:', error);
        setConnectionError(error.message);
        setIsConnected(false);
      });

      socketRef.current = newSocket;
      setSocket(newSocket);
    }

    return () => {
      if (socketRef.current) {
        console.log('Cleaning up socket connection...');
        socketRef.current.disconnect();
        socketRef.current = null;
        setSocket(null);
        setIsConnected(false);
      }
    };
  }, [session, status]);

  const joinChat = (chatId: string) => {
    if (socket && isConnected) {
      socket.emit('join-chat', chatId);
    }
  };

  const leaveChat = (chatId: string) => {
    if (socket && isConnected) {
      socket.emit('leave-chat', chatId);
    }
  };

  const sendMessage = (chatId: string, content: string, attachments?: { type: string; url: string; name: string }[]) => {
    if (socket && isConnected) {
      socket.emit('send-message', { chatId, content, attachments });
    }
  };

  const startTyping = (chatId: string) => {
    if (socket && isConnected) {
      socket.emit('typing-start', chatId);
    }
  };

  const stopTyping = (chatId: string) => {
    if (socket && isConnected) {
      socket.emit('typing-stop', chatId);
    }
  };

  const markMessagesAsRead = (chatId: string) => {
    if (socket && isConnected) {
      socket.emit('mark-messages-read', chatId);
    }
  };

  const on = <K extends keyof SocketEvents>(event: K, callback: SocketEvents[K]) => {
    if (socket) {
      socket.on(event, callback);
    }
  };

  const off = <K extends keyof SocketEvents>(event: K, callback?: SocketEvents[K]) => {
    if (socket) {
      socket.off(event, callback);
    }
  };

  return {
    socket,
    isConnected,
    connectionError,
    joinChat,
    leaveChat,
    sendMessage,
    startTyping,
    stopTyping,
    markMessagesAsRead,
    on,
    off,
  };
}

export default useSocket;

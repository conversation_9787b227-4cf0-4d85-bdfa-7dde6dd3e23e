"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/categories/[slug]/attributes/route";
exports.ids = ["app/api/categories/[slug]/attributes/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcategories%2F%5Bslug%5D%2Fattributes%2Froute&page=%2Fapi%2Fcategories%2F%5Bslug%5D%2Fattributes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcategories%2F%5Bslug%5D%2Fattributes%2Froute.ts&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcategories%2F%5Bslug%5D%2Fattributes%2Froute&page=%2Fapi%2Fcategories%2F%5Bslug%5D%2Fattributes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcategories%2F%5Bslug%5D%2Fattributes%2Froute.ts&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_mac_Documents_AI_Development_marketplace_src_app_api_categories_slug_attributes_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/categories/[slug]/attributes/route.ts */ \"(rsc)/./src/app/api/categories/[slug]/attributes/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/categories/[slug]/attributes/route\",\n        pathname: \"/api/categories/[slug]/attributes\",\n        filename: \"route\",\n        bundlePath: \"app/api/categories/[slug]/attributes/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/api/categories/[slug]/attributes/route.ts\",\n    nextConfigOutput,\n    userland: _Users_mac_Documents_AI_Development_marketplace_src_app_api_categories_slug_attributes_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/categories/[slug]/attributes/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZjYXRlZ29yaWVzJTJGJTVCc2x1ZyU1RCUyRmF0dHJpYnV0ZXMlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRmNhdGVnb3JpZXMlMkYlNUJzbHVnJTVEJTJGYXR0cmlidXRlcyUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRmNhdGVnb3JpZXMlMkYlNUJzbHVnJTVEJTJGYXR0cmlidXRlcyUyRnJvdXRlLnRzJmFwcERpcj0lMkZVc2VycyUyRm1hYyUyRkRvY3VtZW50cyUyRkFJJTIwRGV2ZWxvcG1lbnQlMkZtYXJrZXRwbGFjZSUyRnNyYyUyRmFwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9JTJGVXNlcnMlMkZtYWMlMkZEb2N1bWVudHMlMkZBSSUyMERldmVsb3BtZW50JTJGbWFya2V0cGxhY2UmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXNHO0FBQ3ZDO0FBQ2M7QUFDaUQ7QUFDOUg7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGdIQUFtQjtBQUMzQztBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSx1R0FBdUc7QUFDL0c7QUFDQTtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUM2Sjs7QUFFN0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJrZXRwbGFjZS8/ZjlhMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCIvVXNlcnMvbWFjL0RvY3VtZW50cy9BSSBEZXZlbG9wbWVudC9tYXJrZXRwbGFjZS9zcmMvYXBwL2FwaS9jYXRlZ29yaWVzL1tzbHVnXS9hdHRyaWJ1dGVzL3JvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9jYXRlZ29yaWVzL1tzbHVnXS9hdHRyaWJ1dGVzL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvY2F0ZWdvcmllcy9bc2x1Z10vYXR0cmlidXRlc1wiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvY2F0ZWdvcmllcy9bc2x1Z10vYXR0cmlidXRlcy9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIi9Vc2Vycy9tYWMvRG9jdW1lbnRzL0FJIERldmVsb3BtZW50L21hcmtldHBsYWNlL3NyYy9hcHAvYXBpL2NhdGVnb3JpZXMvW3NsdWddL2F0dHJpYnV0ZXMvcm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgaGVhZGVySG9va3MsIHN0YXRpY0dlbmVyYXRpb25CYWlsb3V0IH0gPSByb3V0ZU1vZHVsZTtcbmNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9hcGkvY2F0ZWdvcmllcy9bc2x1Z10vYXR0cmlidXRlcy9yb3V0ZVwiO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICBzZXJ2ZXJIb29rcyxcbiAgICAgICAgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBoZWFkZXJIb29rcywgc3RhdGljR2VuZXJhdGlvbkJhaWxvdXQsIG9yaWdpbmFsUGF0aG5hbWUsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcategories%2F%5Bslug%5D%2Fattributes%2Froute&page=%2Fapi%2Fcategories%2F%5Bslug%5D%2Fattributes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcategories%2F%5Bslug%5D%2Fattributes%2Froute.ts&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/categories/[slug]/attributes/route.ts":
/*!***********************************************************!*\
  !*** ./src/app/api/categories/[slug]/attributes/route.ts ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\nasync function GET(request, { params }) {\n    try {\n        const { slug } = params;\n        // Find the category\n        const category = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.category.findUnique({\n            where: {\n                slug\n            }\n        });\n        if (!category) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Category not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Get category attributes from the database\n        const categoryAttributes = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.categoryAttribute.findMany({\n            where: {\n                categoryId: category.id\n            },\n            orderBy: {\n                name: \"asc\"\n            }\n        });\n        // If no attributes found in database, return hardcoded attributes for demo\n        if (categoryAttributes.length === 0) {\n            const hardcodedAttributes = getHardcodedAttributes(slug);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(hardcodedAttributes);\n        }\n        // Transform database attributes to expected format\n        const transformedAttributes = categoryAttributes.map((attr)=>({\n                id: attr.name.toLowerCase().replace(/\\s+/g, \"\"),\n                name: attr.name,\n                type: attr.type.toLowerCase(),\n                options: attr.options || [],\n                isRequired: attr.isRequired\n            }));\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(transformedAttributes);\n    } catch (error) {\n        console.error(\"Error fetching category attributes:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nfunction getHardcodedAttributes(categorySlug) {\n    if (categorySlug.includes(\"real-estate\") || categorySlug.includes(\"houses\") || categorySlug.includes(\"apartments\")) {\n        return [\n            {\n                id: \"bedrooms\",\n                name: \"Bedrooms\",\n                type: \"number\",\n                options: [],\n                isRequired: false\n            },\n            {\n                id: \"bathrooms\",\n                name: \"Bathrooms\",\n                type: \"number\",\n                options: [],\n                isRequired: false\n            },\n            {\n                id: \"squarefeet\",\n                name: \"Square Feet\",\n                type: \"number\",\n                options: [],\n                isRequired: false\n            },\n            {\n                id: \"propertytype\",\n                name: \"Property Type\",\n                type: \"select\",\n                options: [\n                    \"Single Family\",\n                    \"Duplex\",\n                    \"Apartment\",\n                    \"Townhouse\",\n                    \"Condo\",\n                    \"Commercial\"\n                ],\n                isRequired: false\n            },\n            {\n                id: \"furnished\",\n                name: \"Furnished\",\n                type: \"boolean\",\n                options: [],\n                isRequired: false\n            },\n            {\n                id: \"parking\",\n                name: \"Parking Available\",\n                type: \"boolean\",\n                options: [],\n                isRequired: false\n            },\n            {\n                id: \"pool\",\n                name: \"Swimming Pool\",\n                type: \"boolean\",\n                options: [],\n                isRequired: false\n            }\n        ];\n    } else if (categorySlug.includes(\"vehicles\") || categorySlug.includes(\"cars\")) {\n        return [\n            {\n                id: \"make\",\n                name: \"Make\",\n                type: \"select\",\n                options: [\n                    \"Toyota\",\n                    \"Honda\",\n                    \"BMW\",\n                    \"Mercedes-Benz\",\n                    \"Lexus\",\n                    \"Audi\",\n                    \"Ford\",\n                    \"Chevrolet\",\n                    \"Nissan\",\n                    \"Hyundai\"\n                ],\n                isRequired: false\n            },\n            {\n                id: \"year\",\n                name: \"Year\",\n                type: \"number\",\n                options: [],\n                isRequired: false\n            },\n            {\n                id: \"mileage\",\n                name: \"Mileage\",\n                type: \"number\",\n                options: [],\n                isRequired: false\n            },\n            {\n                id: \"fueltype\",\n                name: \"Fuel Type\",\n                type: \"select\",\n                options: [\n                    \"Gasoline\",\n                    \"Diesel\",\n                    \"Electric\",\n                    \"Hybrid\",\n                    \"Plug-in Hybrid\"\n                ],\n                isRequired: false\n            },\n            {\n                id: \"transmission\",\n                name: \"Transmission\",\n                type: \"select\",\n                options: [\n                    \"Automatic\",\n                    \"Manual\",\n                    \"CVT\"\n                ],\n                isRequired: false\n            },\n            {\n                id: \"drivetrain\",\n                name: \"Drivetrain\",\n                type: \"select\",\n                options: [\n                    \"FWD\",\n                    \"RWD\",\n                    \"AWD\",\n                    \"4WD\"\n                ],\n                isRequired: false\n            }\n        ];\n    } else if (categorySlug.includes(\"gadgets\") || categorySlug.includes(\"smartphones\") || categorySlug.includes(\"laptops\")) {\n        return [\n            {\n                id: \"brand\",\n                name: \"Brand\",\n                type: \"select\",\n                options: [\n                    \"Apple\",\n                    \"Samsung\",\n                    \"Google\",\n                    \"OnePlus\",\n                    \"Dell\",\n                    \"HP\",\n                    \"Lenovo\",\n                    \"ASUS\",\n                    \"Microsoft\"\n                ],\n                isRequired: false\n            },\n            {\n                id: \"storage\",\n                name: \"Storage\",\n                type: \"select\",\n                options: [\n                    \"64GB\",\n                    \"128GB\",\n                    \"256GB\",\n                    \"512GB\",\n                    \"1TB\",\n                    \"2TB\"\n                ],\n                isRequired: false\n            },\n            {\n                id: \"ram\",\n                name: \"RAM\",\n                type: \"select\",\n                options: [\n                    \"4GB\",\n                    \"8GB\",\n                    \"16GB\",\n                    \"32GB\",\n                    \"64GB\"\n                ],\n                isRequired: false\n            },\n            {\n                id: \"screensize\",\n                name: \"Screen Size\",\n                type: \"select\",\n                options: [\n                    '5.5\"',\n                    '6.1\"',\n                    '6.7\"',\n                    '13\"',\n                    '14\"',\n                    '15\"',\n                    '16\"'\n                ],\n                isRequired: false\n            },\n            {\n                id: \"warranty\",\n                name: \"Warranty\",\n                type: \"boolean\",\n                options: [],\n                isRequired: false\n            },\n            {\n                id: \"unlocked\",\n                name: \"Unlocked\",\n                type: \"boolean\",\n                options: [],\n                isRequired: false\n            }\n        ];\n    }\n    return [];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/categories/[slug]/attributes/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n// For backward compatibility\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prisma);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQThDO0FBRTlDLE1BQU1DLGtCQUFrQkM7QUFJakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUc7QUFFbkUsSUFBSUksSUFBeUIsRUFBY0gsZ0JBQWdCRSxNQUFNLEdBQUdBO0FBRXBFLDZCQUE2QjtBQUM3QixpRUFBZUEsTUFBTUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcmtldHBsYWNlLy4vc3JjL2xpYi9wcmlzbWEudHM/MDFkNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCc7XG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkO1xufTtcblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYTtcblxuLy8gRm9yIGJhY2t3YXJkIGNvbXBhdGliaWxpdHlcbmV4cG9ydCBkZWZhdWx0IHByaXNtYTsiXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcategories%2F%5Bslug%5D%2Fattributes%2Froute&page=%2Fapi%2Fcategories%2F%5Bslug%5D%2Fattributes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcategories%2F%5Bslug%5D%2Fattributes%2Froute.ts&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
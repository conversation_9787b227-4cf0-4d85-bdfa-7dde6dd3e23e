"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/products/route";
exports.ids = ["app/api/products/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_mac_Documents_AI_Development_marketplace_src_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/products/route.ts */ \"(rsc)/./src/app/api/products/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/products/route\",\n        pathname: \"/api/products\",\n        filename: \"route\",\n        bundlePath: \"app/api/products/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/api/products/route.ts\",\n    nextConfigOutput,\n    userland: _Users_mac_Documents_AI_Development_marketplace_src_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/products/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/[...nextauth]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/[...nextauth]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler),\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/prisma-adapter */ \"(rsc)/./node_modules/@auth/prisma-adapter/index.js\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/next */ \"(rsc)/./node_modules/next-auth/next/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n\n\n\n\nconst authOptions = {\n    adapter: (0,_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_lib_prisma__WEBPACK_IMPORTED_MODULE_5__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_5__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    }\n                });\n                if (!user || !user?.hashedPassword) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const isCorrectPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"].compare(credentials.password, user.hashedPassword);\n                if (!isCorrectPassword) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                return user;\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/login\"\n    },\n    debug: \"development\" === \"development\",\n    session: {\n        strategy: \"jwt\"\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.role = user.role;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (session.user) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n            }\n            return session;\n        }\n    }\n};\nconst handler = (0,next_auth_next__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/products/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/products/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/next */ \"(rsc)/./node_modules/next-auth/next/index.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _app_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/api/auth/[...nextauth]/route */ \"(rsc)/./src/app/api/auth/[...nextauth]/route.ts\");\n\n\n\n\n// GET endpoint to fetch products with filtering and pagination\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"12\");\n        const category = searchParams.get(\"category\");\n        const search = searchParams.get(\"search\");\n        const minPrice = searchParams.get(\"minPrice\");\n        const maxPrice = searchParams.get(\"maxPrice\");\n        const condition = searchParams.get(\"condition\");\n        const location = searchParams.get(\"location\");\n        const attributes = searchParams.get(\"attributes\");\n        const offset = (page - 1) * limit;\n        // Build where clause\n        const where = {\n            status: \"ACTIVE\",\n            isHidden: false\n        };\n        if (category && category !== \"all\") {\n            where.category = {\n                slug: category\n            };\n        }\n        if (search) {\n            where.OR = [\n                {\n                    name: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    description: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                }\n            ];\n        }\n        if (minPrice || maxPrice) {\n            where.price = {};\n            if (minPrice) where.price.gte = parseFloat(minPrice);\n            if (maxPrice) where.price.lte = parseFloat(maxPrice);\n        }\n        if (condition) {\n            where.condition = condition;\n        }\n        if (location) {\n            where.location = {\n                OR: [\n                    {\n                        city: {\n                            contains: location,\n                            mode: \"insensitive\"\n                        }\n                    },\n                    {\n                        state: {\n                            contains: location,\n                            mode: \"insensitive\"\n                        }\n                    },\n                    {\n                        address: {\n                            contains: location,\n                            mode: \"insensitive\"\n                        }\n                    }\n                ]\n            };\n        }\n        // Handle attribute filtering\n        if (attributes) {\n            try {\n                const attributeFilters = JSON.parse(attributes);\n                const attributeConditions = [];\n                for (const [key, value] of Object.entries(attributeFilters)){\n                    if (value !== null && value !== undefined && value !== \"\") {\n                        // For JSON field queries in Prisma, we need to use path syntax\n                        attributeConditions.push({\n                            attributes: {\n                                path: [\n                                    key\n                                ],\n                                equals: value\n                            }\n                        });\n                    }\n                }\n                if (attributeConditions.length > 0) {\n                    where.AND = attributeConditions;\n                }\n            } catch (error) {\n                console.error(\"Error parsing attribute filters:\", error);\n            }\n        }\n        // Fetch products with pagination\n        const [products, totalCount] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.product.findMany({\n                where,\n                include: {\n                    seller: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true\n                        }\n                    },\n                    category: {\n                        select: {\n                            id: true,\n                            name: true,\n                            slug: true\n                        }\n                    },\n                    location: true,\n                    _count: {\n                        select: {\n                            reviews: true\n                        }\n                    }\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                },\n                skip: offset,\n                take: limit\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.product.count({\n                where\n            })\n        ]);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            products,\n            pagination: {\n                page,\n                limit,\n                totalCount,\n                totalPages: Math.ceil(totalCount / limit)\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching products:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST endpoint to create a new product\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_app_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_3__.authOptions);\n        if (!session?.user) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                message: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { name, description, price, categoryId, condition, images, location, attributes, status } = body;\n        // Validate required fields\n        if (!name || !price || !categoryId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                message: \"Name, price, and category are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate price\n        const numericPrice = parseFloat(price);\n        if (isNaN(numericPrice) || numericPrice <= 0) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                message: \"Price must be a valid positive number\"\n            }, {\n                status: 400\n            });\n        }\n        // Check if category exists\n        const category = await _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.category.findUnique({\n            where: {\n                id: categoryId\n            }\n        });\n        if (!category) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                message: \"Category not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Handle location if provided\n        let locationId = null;\n        if (location && location.address) {\n            // Check if location already exists\n            const existingLocation = await _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.location.findFirst({\n                where: {\n                    address: location.address,\n                    city: location.city,\n                    state: location.state,\n                    country: location.country\n                }\n            });\n            if (existingLocation) {\n                locationId = existingLocation.id;\n            } else {\n                // Create new location\n                const newLocation = await _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.location.create({\n                    data: {\n                        address: location.address,\n                        city: location.city,\n                        state: location.state || \"\",\n                        country: location.country,\n                        postalCode: location.postalCode || \"\",\n                        latitude: location.latitude || 0,\n                        longitude: location.longitude || 0\n                    }\n                });\n                locationId = newLocation.id;\n            }\n        }\n        // Create the product\n        const product = await _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.product.create({\n            data: {\n                name,\n                description: description || null,\n                price: numericPrice,\n                categoryId,\n                condition: condition || \"new\",\n                images: images || [],\n                sellerId: session.user.id,\n                locationId,\n                status: status || \"ACTIVE\"\n            },\n            include: {\n                seller: {\n                    select: {\n                        id: true,\n                        name: true,\n                        image: true\n                    }\n                },\n                category: {\n                    select: {\n                        id: true,\n                        name: true,\n                        slug: true,\n                        icon: true\n                    }\n                },\n                location: true\n            }\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(product, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Error creating product:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/products/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n// For backward compatibility\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prisma);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQThDO0FBRTlDLE1BQU1DLGtCQUFrQkM7QUFJakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUc7QUFFbkUsSUFBSUksSUFBeUIsRUFBY0gsZ0JBQWdCRSxNQUFNLEdBQUdBO0FBRXBFLDZCQUE2QjtBQUM3QixpRUFBZUEsTUFBTUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcmtldHBsYWNlLy4vc3JjL2xpYi9wcmlzbWEudHM/MDFkNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCc7XG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkO1xufTtcblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYTtcblxuLy8gRm9yIGJhY2t3YXJkIGNvbXBhdGliaWxpdHlcbmV4cG9ydCBkZWZhdWx0IHByaXNtYTsiXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/cookie","vendor-chunks/@auth","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
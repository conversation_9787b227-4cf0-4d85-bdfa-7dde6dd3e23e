import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('🏷️ Adding category-specific attributes to products...');

    // Get all products with their categories
    const products = await prisma.product.findMany({
      include: {
        category: true,
      },
    });

    console.log(`Found ${products.length} products to update`);

    for (const product of products) {
      let attributes: Record<string, any> = {};

      // Add attributes based on category
      if (product.category.slug === 'houses' || product.category.slug === 'apartments' || product.category.slug === 'real-estate') {
        // Real Estate attributes
        if (product.name.includes('4-Bedroom') || product.name.includes('5-Bedroom')) {
          attributes = {
            bedrooms: product.name.includes('4-Bedroom') ? 4 : 5,
            bathrooms: product.name.includes('4-Bedroom') ? 3 : 4,
            squareFeet: product.name.includes('4-Bedroom') ? 2800 : 4200,
            yearBuilt: 2022,
            propertyType: product.name.includes('Duplex') ? 'Duplex' : product.name.includes('Mansion') ? 'Single Family' : 'Apartment',
            furnished: product.name.includes('furnished') || product.name.includes('Luxury'),
            parking: true,
            pool: product.name.includes('Pool') || product.name.includes('Luxury'),
            garden: product.name.includes('Mansion') || product.name.includes('Villa'),
          };
        } else if (product.name.includes('3-Bedroom')) {
          attributes = {
            bedrooms: 3,
            bathrooms: 2,
            squareFeet: 1800,
            yearBuilt: 2021,
            propertyType: 'Apartment',
            furnished: product.name.includes('Modern'),
            parking: true,
            pool: false,
            garden: false,
          };
        } else if (product.name.includes('2-Bedroom')) {
          attributes = {
            bedrooms: 2,
            bathrooms: 1,
            squareFeet: 1200,
            yearBuilt: 2018,
            propertyType: 'Apartment',
            furnished: false,
            parking: true,
            pool: false,
            garden: false,
          };
        } else if (product.name.includes('Commercial') || product.name.includes('Office')) {
          attributes = {
            propertyType: 'Commercial',
            squareFeet: 2500,
            yearBuilt: 2020,
            parking: true,
            furnished: true,
            officeSpaces: 8,
            conferenceRooms: 2,
          };
        }
      } else if (product.category.slug === 'cars' || product.category.slug === 'vehicles') {
        // Vehicle attributes
        if (product.name.includes('Toyota Camry')) {
          attributes = {
            make: 'Toyota',
            model: 'Camry',
            year: product.name.includes('2023') ? 2023 : product.name.includes('2021') ? 2021 : 2022,
            mileage: product.name.includes('2023') ? 8500 : product.name.includes('2021') ? 35000 : 15000,
            fuelType: 'Gasoline',
            transmission: 'Automatic',
            engineSize: '2.5L',
            doors: 4,
            color: 'Silver',
            drivetrain: 'FWD',
            mpg: '28/39',
          };
        } else if (product.name.includes('BMW X5')) {
          attributes = {
            make: 'BMW',
            model: 'X5',
            year: 2022,
            mileage: 22000,
            fuelType: 'Gasoline',
            transmission: 'Automatic',
            engineSize: '3.0L',
            doors: 4,
            color: 'Black',
            drivetrain: 'AWD',
            mpg: '21/26',
          };
        } else if (product.name.includes('Mercedes-Benz C-Class')) {
          attributes = {
            make: 'Mercedes-Benz',
            model: 'C-Class',
            year: 2024,
            mileage: 500,
            fuelType: 'Gasoline',
            transmission: 'Automatic',
            engineSize: '2.0L Turbo',
            doors: 4,
            color: 'White',
            drivetrain: 'RWD',
            mpg: '23/32',
          };
        } else if (product.name.includes('Honda Accord')) {
          attributes = {
            make: 'Honda',
            model: 'Accord',
            year: 2021,
            mileage: 28000,
            fuelType: 'Gasoline',
            transmission: 'Automatic',
            engineSize: '1.5L Turbo',
            doors: 4,
            color: 'Blue',
            drivetrain: 'FWD',
            mpg: '30/38',
          };
        } else if (product.name.includes('Lexus RX')) {
          attributes = {
            make: 'Lexus',
            model: 'RX 350',
            year: 2023,
            mileage: 12000,
            fuelType: 'Gasoline',
            transmission: 'Automatic',
            engineSize: '3.5L V6',
            doors: 4,
            color: 'Pearl White',
            drivetrain: 'AWD',
            mpg: '20/27',
          };
        }
      } else if (product.category.slug === 'smartphones' || product.category.slug === 'gadgets') {
        // Smartphone/Gadget attributes
        if (product.name.includes('iPhone 15 Pro Max')) {
          attributes = {
            brand: 'Apple',
            model: 'iPhone 15 Pro Max',
            storage: '256GB',
            color: 'Titanium Blue',
            screenSize: '6.7"',
            operatingSystem: 'iOS 17',
            processor: 'A17 Pro',
            camera: '48MP Pro',
            battery: '4441mAh',
            warranty: true,
            unlocked: true,
          };
        } else if (product.name.includes('Samsung Galaxy S24')) {
          attributes = {
            brand: 'Samsung',
            model: 'Galaxy S24 Ultra',
            storage: '512GB',
            color: 'Titanium Gray',
            screenSize: '6.8"',
            operatingSystem: 'Android 14',
            processor: 'Snapdragon 8 Gen 3',
            camera: '200MP',
            battery: '5000mAh',
            warranty: true,
            unlocked: true,
          };
        } else if (product.name.includes('MacBook Pro')) {
          attributes = {
            brand: 'Apple',
            model: 'MacBook Pro 16"',
            processor: 'M3 Max',
            ram: '36GB',
            storage: '1TB SSD',
            screenSize: '16"',
            color: 'Space Black',
            operatingSystem: 'macOS Sonoma',
            graphics: 'M3 Max GPU',
            warranty: true,
            ports: 'Thunderbolt 4, HDMI, SD Card',
          };
        } else if (product.name.includes('Dell XPS')) {
          attributes = {
            brand: 'Dell',
            model: 'XPS 15',
            processor: 'Intel Core i7',
            ram: '32GB',
            storage: '1TB SSD',
            screenSize: '15.6"',
            color: 'Platinum Silver',
            operatingSystem: 'Windows 11',
            graphics: 'NVIDIA RTX 4060',
            warranty: true,
            ports: 'USB-C, USB-A, HDMI, SD Card',
          };
        } else if (product.name.includes('iPad Pro')) {
          attributes = {
            brand: 'Apple',
            model: 'iPad Pro 12.9"',
            processor: 'M2',
            storage: '256GB',
            screenSize: '12.9"',
            color: 'Space Gray',
            operatingSystem: 'iPadOS 17',
            connectivity: 'WiFi + Cellular',
            camera: '12MP',
            warranty: true,
            accessories: 'Apple Pencil Compatible',
          };
        } else if (product.name.includes('Apple Watch')) {
          attributes = {
            brand: 'Apple',
            model: 'Apple Watch Series 9',
            size: '45mm',
            color: 'Midnight',
            connectivity: 'GPS + Cellular',
            operatingSystem: 'watchOS 10',
            battery: '18 hours',
            waterResistant: true,
            warranty: true,
            band: 'Sport Band',
          };
        }
      }

      // Update product with attributes
      if (Object.keys(attributes).length > 0) {
        await prisma.product.update({
          where: { id: product.id },
          data: {
            attributes: attributes,
          },
        });

        console.log(`✅ Updated ${product.name} with ${Object.keys(attributes).length} attributes`);
      }
    }

    console.log('🎉 Product attributes update completed!');

  } catch (error) {
    console.error('❌ Error updating product attributes:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });

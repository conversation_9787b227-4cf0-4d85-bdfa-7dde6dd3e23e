import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('🌱 Starting demo product seeding for Real Estate, Cars, and Gadgets...');

    // Get existing users and categories
    const users = await prisma.user.findMany();
    const categories = await prisma.category.findMany();

    if (users.length === 0) {
      console.log('❌ No users found. Please run user seeding first.');
      return;
    }

    // Find category IDs
    const realEstateCategory = categories.find(c => c.slug === 'real-estate');
    const vehiclesCategory = categories.find(c => c.slug === 'vehicles');
    const gadgetsCategory = categories.find(c => c.slug === 'gadgets');
    const housesCategory = categories.find(c => c.slug === 'houses');
    const apartmentsCategory = categories.find(c => c.slug === 'apartments');
    const carsCategory = categories.find(c => c.slug === 'cars');
    const smartphonesCategory = categories.find(c => c.slug === 'smartphones');
    const laptopsCategory = categories.find(c => c.slug === 'laptops');

    // Create locations first
    const locations = await Promise.all([
      prisma.location.create({
        data: {
          address: '123 Victoria Island Road',
          city: 'Lagos',
          state: 'Lagos State',
          country: 'Nigeria',
          postalCode: '101001',
          latitude: 6.4281,
          longitude: 3.4219,
        },
      }),
      prisma.location.create({
        data: {
          address: '456 Maitama District',
          city: 'Abuja',
          state: 'FCT',
          country: 'Nigeria',
          postalCode: '900001',
          latitude: 9.0579,
          longitude: 7.4951,
        },
      }),
      prisma.location.create({
        data: {
          address: '789 GRA Phase 2',
          city: 'Port Harcourt',
          state: 'Rivers State',
          country: 'Nigeria',
          postalCode: '500001',
          latitude: 4.8156,
          longitude: 7.0498,
        },
      }),
    ]);

    // Real Estate Products
    const realEstateProducts = [
      {
        name: 'Luxury 4-Bedroom Duplex in Victoria Island',
        description: 'Stunning modern duplex with panoramic city views, fully furnished, swimming pool, gym, and 24/7 security. Perfect for executives and families.',
        price: 850000.00,
        condition: 'NEW' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/106399/pexels-photo-106399.jpeg',
          'https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg',
          'https://images.pexels.com/photos/1643383/pexels-photo-1643383.jpeg'
        ],
        sellerId: users[0].id,
        categoryId: housesCategory?.id || realEstateCategory?.id || categories[0].id,
        locationId: locations[0].id,
        viewCount: 156,
        featuredUntil: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
      },
      {
        name: 'Modern 3-Bedroom Apartment in Maitama',
        description: 'Spacious apartment in prestigious Maitama district. Features include marble floors, central AC, fitted kitchen, and parking space.',
        price: 450000.00,
        condition: 'NEW' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg',
          'https://images.pexels.com/photos/1571453/pexels-photo-1571453.jpeg'
        ],
        sellerId: users[1].id,
        categoryId: apartmentsCategory?.id || realEstateCategory?.id || categories[0].id,
        locationId: locations[1].id,
        viewCount: 89,
      },
      {
        name: 'Executive 5-Bedroom Mansion with Pool',
        description: 'Magnificent mansion in exclusive GRA neighborhood. Features include swimming pool, tennis court, staff quarters, and beautiful gardens.',
        price: 1200000.00,
        condition: 'NEW' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/1396132/pexels-photo-1396132.jpeg',
          'https://images.pexels.com/photos/1396125/pexels-photo-1396125.jpeg'
        ],
        sellerId: users[2].id,
        categoryId: housesCategory?.id || realEstateCategory?.id || categories[0].id,
        locationId: locations[2].id,
        viewCount: 234,
        featuredUntil: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000),
      },
    ];

    // Vehicle Products
    const vehicleProducts = [
      {
        name: '2023 Toyota Camry XLE - Like New',
        description: 'Pristine condition Toyota Camry with only 8,500 miles. Features leather seats, navigation system, backup camera, and full warranty.',
        price: 42000.00,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/3802510/pexels-photo-3802510.jpeg',
          'https://images.pexels.com/photos/1545743/pexels-photo-1545743.jpeg'
        ],
        sellerId: users[0].id,
        categoryId: carsCategory?.id || vehiclesCategory?.id || categories[0].id,
        locationId: locations[0].id,
        viewCount: 178,
        featuredUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      },
      {
        name: '2022 BMW X5 M Sport Package',
        description: 'Luxury SUV with M Sport package, premium sound system, panoramic sunroof, and advanced safety features. Excellent condition.',
        price: 75000.00,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/3802508/pexels-photo-3802508.jpeg',
          'https://images.pexels.com/photos/1592384/pexels-photo-1592384.jpeg'
        ],
        sellerId: users[1].id,
        categoryId: carsCategory?.id || vehiclesCategory?.id || categories[0].id,
        locationId: locations[1].id,
        viewCount: 145,
      },
      {
        name: '2024 Mercedes-Benz C-Class AMG',
        description: 'Brand new Mercedes C-Class with AMG styling package. Turbocharged engine, premium interior, and cutting-edge technology.',
        price: 95000.00,
        condition: 'NEW' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/3802508/pexels-photo-3802508.jpeg',
          'https://images.pexels.com/photos/1592384/pexels-photo-1592384.jpeg'
        ],
        sellerId: users[2].id,
        categoryId: carsCategory?.id || vehiclesCategory?.id || categories[0].id,
        locationId: locations[2].id,
        viewCount: 267,
        featuredUntil: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000),
      },
    ];

    // Gadget Products
    const gadgetProducts = [
      {
        name: 'iPhone 15 Pro Max 256GB - Titanium Blue',
        description: 'Latest iPhone 15 Pro Max with titanium build, A17 Pro chip, Pro camera system, and Action Button. Unlocked and brand new.',
        price: 1299.00,
        condition: 'NEW' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/788946/pexels-photo-788946.jpeg',
          'https://images.pexels.com/photos/1092644/pexels-photo-1092644.jpeg'
        ],
        sellerId: users[0].id,
        categoryId: smartphonesCategory?.id || gadgetsCategory?.id || categories[0].id,
        locationId: locations[0].id,
        viewCount: 312,
        featuredUntil: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
      },
      {
        name: 'MacBook Pro 16" M3 Max - Space Black',
        description: 'Powerful MacBook Pro with M3 Max chip, 36GB RAM, 1TB SSD. Perfect for professionals, creators, and developers. Like new condition.',
        price: 3499.00,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/205421/pexels-photo-205421.jpeg',
          'https://images.pexels.com/photos/1029757/pexels-photo-1029757.jpeg'
        ],
        sellerId: users[1].id,
        categoryId: laptopsCategory?.id || gadgetsCategory?.id || categories[0].id,
        locationId: locations[1].id,
        viewCount: 198,
      },
      {
        name: 'Samsung Galaxy S24 Ultra 512GB',
        description: 'Premium Samsung flagship with S Pen, 200MP camera, 120Hz display, and titanium frame. Excellent for photography and productivity.',
        price: 1199.00,
        condition: 'NEW' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/788946/pexels-photo-788946.jpeg',
          'https://images.pexels.com/photos/1092644/pexels-photo-1092644.jpeg'
        ],
        sellerId: users[2].id,
        categoryId: smartphonesCategory?.id || gadgetsCategory?.id || categories[0].id,
        locationId: locations[2].id,
        viewCount: 156,
        featuredUntil: new Date(Date.now() + 12 * 24 * 60 * 60 * 1000),
      },
    ];

    // Additional Real Estate Products
    const moreRealEstateProducts = [
      {
        name: 'Commercial Office Space - Ikeja',
        description: 'Prime commercial office space in Ikeja business district. 2,500 sq ft, modern facilities, parking, and excellent accessibility.',
        price: 320000.00,
        condition: 'NEW' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/380769/pexels-photo-380769.jpeg',
          'https://images.pexels.com/photos/1170412/pexels-photo-1170412.jpeg'
        ],
        sellerId: users[0].id,
        categoryId: realEstateCategory?.id || categories[0].id,
        locationId: locations[0].id,
        viewCount: 67,
      },
      {
        name: 'Affordable 2-Bedroom Flat - Surulere',
        description: 'Well-maintained 2-bedroom apartment in family-friendly Surulere neighborhood. Close to schools, markets, and transport.',
        price: 180000.00,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg'
        ],
        sellerId: users[1].id,
        categoryId: apartmentsCategory?.id || realEstateCategory?.id || categories[0].id,
        locationId: locations[0].id,
        viewCount: 123,
      },
    ];

    // Additional Vehicle Products
    const moreVehicleProducts = [
      {
        name: '2021 Honda Accord Sport - Excellent Condition',
        description: 'Reliable Honda Accord with sport package, turbocharged engine, and premium features. Well-maintained with service records.',
        price: 38000.00,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/3802510/pexels-photo-3802510.jpeg'
        ],
        sellerId: users[2].id,
        categoryId: carsCategory?.id || vehiclesCategory?.id || categories[0].id,
        locationId: locations[1].id,
        viewCount: 92,
      },
      {
        name: '2023 Lexus RX 350 - Luxury SUV',
        description: 'Premium Lexus SUV with luxury package, heated/cooled seats, premium audio, and advanced safety systems.',
        price: 68000.00,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/1592384/pexels-photo-1592384.jpeg'
        ],
        sellerId: users[0].id,
        categoryId: carsCategory?.id || vehiclesCategory?.id || categories[0].id,
        locationId: locations[2].id,
        viewCount: 134,
      },
    ];

    // Additional Gadget Products
    const moreGadgetProducts = [
      {
        name: 'iPad Pro 12.9" M2 - 256GB WiFi + Cellular',
        description: 'Latest iPad Pro with M2 chip, Liquid Retina XDR display, and Apple Pencil support. Perfect for creative professionals.',
        price: 1099.00,
        condition: 'NEW' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/1334597/pexels-photo-1334597.jpeg'
        ],
        sellerId: users[1].id,
        categoryId: gadgetsCategory?.id || categories[0].id,
        locationId: locations[0].id,
        viewCount: 87,
      },
      {
        name: 'Dell XPS 15 - Developer Edition',
        description: 'High-performance laptop with Intel i7, 32GB RAM, 1TB SSD, and 4K display. Ideal for developers and content creators.',
        price: 2299.00,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/205421/pexels-photo-205421.jpeg'
        ],
        sellerId: users[2].id,
        categoryId: laptopsCategory?.id || gadgetsCategory?.id || categories[0].id,
        locationId: locations[1].id,
        viewCount: 156,
      },
      {
        name: 'Apple Watch Series 9 - 45mm GPS + Cellular',
        description: 'Latest Apple Watch with health monitoring, fitness tracking, and cellular connectivity. Includes sport band and charger.',
        price: 499.00,
        condition: 'NEW' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/437037/pexels-photo-437037.jpeg'
        ],
        sellerId: users[0].id,
        categoryId: gadgetsCategory?.id || categories[0].id,
        locationId: locations[2].id,
        viewCount: 78,
      },
    ];

    // Combine all products
    const allProducts = [
      ...realEstateProducts,
      ...vehicleProducts,
      ...gadgetProducts,
      ...moreRealEstateProducts,
      ...moreVehicleProducts,
      ...moreGadgetProducts
    ];

    console.log(`Creating ${allProducts.length} demo products...`);

    const createdProducts = await Promise.all(
      allProducts.map(product => prisma.product.create({ data: product }))
    );

    console.log('✅ Created demo products');

    // Create product views for engagement
    const productViews = [];
    for (const product of createdProducts) {
      for (const user of users) {
        if (Math.random() > 0.4) { // 60% chance of viewing
          productViews.push({
            userId: user.id,
            productId: product.id,
          });
        }
      }
    }

    await Promise.all(
      productViews.map(view =>
        prisma.productView.create({ data: view }).catch(() => {
          // Ignore unique constraint errors
        })
      )
    );

    console.log('✅ Created product views');

    console.log('🎉 Demo product seeding completed successfully!');
    console.log(`Created:
    - ${createdProducts.length} demo products
    - ${productViews.length} product views
    - ${locations.length} locations`);

  } catch (error) {
    console.error('❌ Error seeding demo products:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });

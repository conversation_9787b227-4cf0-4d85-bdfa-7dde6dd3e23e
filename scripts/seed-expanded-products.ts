import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedExpandedProducts() {
  try {
    console.log('🌱 Starting expanded product seeding...');

    // Get existing users and categories
    const users = await prisma.user.findMany();
    const categories = await prisma.category.findMany();
    
    if (users.length === 0 || categories.length === 0) {
      console.log('❌ No users or categories found. Please run basic seeding first.');
      return;
    }

    // Create more diverse products
    const expandedProducts = [
      // Electronics
      {
        name: 'Samsung 4K Smart TV 55"',
        description: 'Crystal clear 4K display with smart features. Perfect for streaming and gaming.',
        price: 649.99,
        condition: 'NEW' as const,
        status: 'ACTIVE' as const,
        images: ['https://images.pexels.com/photos/1444416/pexels-photo-1444416.jpeg'],
        sellerId: users[0].id,
        categoryId: categories.find(c => c.name.includes('Electronics'))?.id || categories[0].id,
        viewCount: 89,
        featuredUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Featured for 7 days
      },
      {
        name: 'iPad Pro 12.9" M2',
        description: 'Latest iPad Pro with M2 chip. 512GB storage, WiFi + Cellular.',
        price: 1299.99,
        condition: 'NEW' as const,
        status: 'ACTIVE' as const,
        images: [
          'https://images.pexels.com/photos/1334597/pexels-photo-1334597.jpeg',
          'https://images.pexels.com/photos/1334598/pexels-photo-1334598.jpeg'
        ],
        sellerId: users[1].id,
        categoryId: categories.find(c => c.name.includes('Electronics'))?.id || categories[0].id,
        viewCount: 156,
      },
      {
        name: 'Gaming Desktop PC',
        description: 'High-performance gaming PC. RTX 4070, 32GB RAM, 1TB SSD. Ready to game!',
        price: 1899.99,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: ['https://images.pexels.com/photos/2582937/pexels-photo-2582937.jpeg'],
        sellerId: users[0].id,
        categoryId: categories.find(c => c.name.includes('Electronics'))?.id || categories[0].id,
        viewCount: 234,
      },
      {
        name: 'Canon EOS R5 Camera',
        description: 'Professional mirrorless camera with 45MP sensor. Includes 24-70mm lens.',
        price: 2899.99,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: ['https://images.pexels.com/photos/90946/pexels-photo-90946.jpeg'],
        sellerId: users[2].id,
        categoryId: categories.find(c => c.name.includes('Electronics'))?.id || categories[0].id,
        viewCount: 67,
      },

      // Fashion & Accessories
      {
        name: 'Luxury Watch - Rolex Submariner',
        description: 'Authentic Rolex Submariner in excellent condition. Comes with box and papers.',
        price: 8999.99,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: ['https://images.pexels.com/photos/190819/pexels-photo-190819.jpeg'],
        sellerId: users[1].id,
        categoryId: categories.find(c => c.name.includes('Fashion'))?.id || categories[0].id,
        viewCount: 445,
        featuredUntil: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // Featured for 14 days
      },
      {
        name: 'Designer Sneakers - Air Jordan 1',
        description: 'Limited edition Air Jordan 1 Retro High. Size 10. Brand new in box.',
        price: 299.99,
        condition: 'NEW' as const,
        status: 'ACTIVE' as const,
        images: ['https://images.pexels.com/photos/2529148/pexels-photo-2529148.jpeg'],
        sellerId: users[1].id,
        categoryId: categories.find(c => c.name.includes('Fashion'))?.id || categories[0].id,
        viewCount: 178,
      },
      {
        name: 'Vintage Denim Jacket',
        description: 'Classic 90s denim jacket. Size L. Perfect vintage condition.',
        price: 85.00,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: ['https://images.pexels.com/photos/1124465/pexels-photo-1124465.jpeg'],
        sellerId: users[3].id,
        categoryId: categories.find(c => c.name.includes('Fashion'))?.id || categories[0].id,
        viewCount: 92,
      },

      // Home & Garden
      {
        name: 'Modern Dining Table Set',
        description: 'Beautiful oak dining table with 6 chairs. Perfect for family dinners.',
        price: 899.99,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: ['https://images.pexels.com/photos/1080721/pexels-photo-1080721.jpeg'],
        sellerId: users[2].id,
        categoryId: categories.find(c => c.name.includes('Furniture'))?.id || categories[0].id,
        viewCount: 134,
      },
      {
        name: 'Sectional Sofa - Gray',
        description: 'Comfortable L-shaped sectional sofa. Great condition, pet-free home.',
        price: 1299.99,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: ['https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg'],
        sellerId: users[0].id,
        categoryId: categories.find(c => c.name.includes('Furniture'))?.id || categories[0].id,
        viewCount: 203,
      },
      {
        name: 'Garden Tool Set',
        description: 'Complete gardening tool set with storage bag. Perfect for spring gardening.',
        price: 79.99,
        condition: 'NEW' as const,
        status: 'ACTIVE' as const,
        images: ['https://images.pexels.com/photos/4505166/pexels-photo-4505166.jpeg'],
        sellerId: users[3].id,
        categoryId: categories.find(c => c.name.includes('Home'))?.id || categories[0].id,
        viewCount: 45,
      },

      // Sports & Recreation
      {
        name: 'Professional Tennis Racket',
        description: 'Wilson Pro Staff tennis racket. Used by professionals. Excellent condition.',
        price: 189.99,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: ['https://images.pexels.com/photos/209977/pexels-photo-209977.jpeg'],
        sellerId: users[2].id,
        categoryId: categories.find(c => c.name.includes('Sports'))?.id || categories[0].id,
        viewCount: 67,
      },
      {
        name: 'Yoga Mat Set',
        description: 'Premium yoga mat with blocks and strap. Perfect for home workouts.',
        price: 49.99,
        condition: 'NEW' as const,
        status: 'ACTIVE' as const,
        images: ['https://images.pexels.com/photos/4056723/pexels-photo-4056723.jpeg'],
        sellerId: users[1].id,
        categoryId: categories.find(c => c.name.includes('Sports'))?.id || categories[0].id,
        viewCount: 89,
      },
      {
        name: 'Electric Skateboard',
        description: 'High-speed electric skateboard with remote control. 20-mile range.',
        price: 599.99,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: ['https://images.pexels.com/photos/2897883/pexels-photo-2897883.jpeg'],
        sellerId: users[0].id,
        categoryId: categories.find(c => c.name.includes('Sports'))?.id || categories[0].id,
        viewCount: 156,
      },

      // Books & Media
      {
        name: 'Vintage Book Collection',
        description: 'Collection of 50 classic literature books. Great condition, hardcover.',
        price: 299.99,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: ['https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg'],
        sellerId: users[3].id,
        categoryId: categories.find(c => c.name.includes('Books'))?.id || categories[0].id,
        viewCount: 78,
      },
      {
        name: 'Vinyl Record Collection',
        description: 'Rare vinyl records from the 70s and 80s. Over 100 albums included.',
        price: 1299.99,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: ['https://images.pexels.com/photos/167092/pexels-photo-167092.jpeg'],
        sellerId: users[2].id,
        categoryId: categories.find(c => c.name.includes('Music'))?.id || categories[0].id,
        viewCount: 234,
        featuredUntil: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000), // Featured for 10 days
      },

      // Automotive
      {
        name: 'Car Dash Camera',
        description: '4K dash camera with night vision. Easy installation, includes SD card.',
        price: 129.99,
        condition: 'NEW' as const,
        status: 'ACTIVE' as const,
        images: ['https://images.pexels.com/photos/3806288/pexels-photo-3806288.jpeg'],
        sellerId: users[2].id,
        categoryId: categories.find(c => c.name.includes('Vehicles'))?.id || categories[0].id,
        viewCount: 112,
      },
      {
        name: 'Motorcycle Helmet',
        description: 'DOT approved motorcycle helmet. Size L. Excellent safety rating.',
        price: 199.99,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: ['https://images.pexels.com/photos/1119796/pexels-photo-1119796.jpeg'],
        sellerId: users[0].id,
        categoryId: categories.find(c => c.name.includes('Vehicles'))?.id || categories[0].id,
        viewCount: 89,
      },

      // Collectibles
      {
        name: 'Vintage Comic Book Collection',
        description: 'Marvel and DC comics from the 80s and 90s. Mint condition, bagged and boarded.',
        price: 899.99,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: ['https://images.pexels.com/photos/1319854/pexels-photo-1319854.jpeg'],
        sellerId: users[1].id,
        categoryId: categories.find(c => c.name.includes('Collectibles'))?.id || categories[0].id,
        viewCount: 167,
      },
      {
        name: 'Antique Wooden Chess Set',
        description: 'Handcrafted wooden chess set from the 1960s. Beautiful craftsmanship.',
        price: 149.99,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: ['https://images.pexels.com/photos/260024/pexels-photo-260024.jpeg'],
        sellerId: users[3].id,
        categoryId: categories.find(c => c.name.includes('Collectibles'))?.id || categories[0].id,
        viewCount: 56,
      },

      // Tools & Equipment
      {
        name: 'Professional Drill Set',
        description: 'Cordless drill with complete bit set. Perfect for DIY projects.',
        price: 159.99,
        condition: 'USED' as const,
        status: 'ACTIVE' as const,
        images: ['https://images.pexels.com/photos/5691659/pexels-photo-5691659.jpeg'],
        sellerId: users[2].id,
        categoryId: categories.find(c => c.name.includes('Tools'))?.id || categories[0].id,
        viewCount: 78,
      },
    ];

    const createdProducts = await Promise.all(
      expandedProducts.map(product => prisma.product.create({ data: product }))
    );

    console.log('✅ Created expanded product catalog');

    // Create additional product views for the new products
    const additionalViews = [];
    for (const product of createdProducts) {
      for (const user of users) {
        if (Math.random() > 0.3) { // 70% chance of viewing
          additionalViews.push({
            userId: user.id,
            productId: product.id,
          });
        }
      }
    }

    await Promise.all(
      additionalViews.map(view => 
        prisma.productView.create({ data: view }).catch(() => {
          // Ignore unique constraint errors
        })
      )
    );

    console.log('✅ Created additional product views');

    console.log('🎉 Expanded product seeding completed successfully!');
    console.log(`Created:
    - ${createdProducts.length} additional products
    - ${additionalViews.length} additional product views
    - ${createdProducts.filter(p => p.featuredUntil).length} featured products`);

  } catch (error) {
    console.error('❌ Error seeding expanded products:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedExpandedProducts();
